<view class="van-index-bar">
  <slot />

  <view
    wx:if="{{ showSidebar }}"
    class="van-index-bar__sidebar"
    catch:tap="onClick"
    catch:touchmove="onTouchMove"
    catch:touchend="onTouchStop"
    catch:touchcancel="onTouchStop"
  >
    <view
      wx:for="{{ indexList }}"
      wx:key="index"
      class="van-index-bar__index"
      style="z-index: {{ zIndex + 1 }}; color: {{ activeAnchorIndex === index ? highlightColor : '' }}"
      data-index="{{ index }}"
    >
      {{ item }}
    </view>
  </view>
</view>
