<wxs src="../wxs/utils.wxs" module="utils" />
<wxs src="./index.wxs" module="computed" />

<view class="van-dropdown-menu van-dropdown-menu--top-bottom custom-class">
  <view
    wx:for="{{ itemListData }}"
    wx:key="index"
    data-index="{{ index }}"
    class="{{ utils.bem('dropdown-menu__item', { disabled: item.disabled }) }}"
    bind:tap="onTitleTap"
  >
    <view
      class="{{ item.titleClass }} {{ utils.bem('dropdown-menu__title', { active: item.showPopup, down: item.showPopup === (direction === 'down') }) }} title-class"
      style="{{ item.showPopup ? 'color:' + activeColor : '' }}"
    >
      <view class="van-ellipsis">
        {{ computed.displayTitle(item) }}
      </view>
    </view>
  </view>

  <slot />
</view>
