{"version": 3, "sources": ["index.js", "stringify.js", "utils.js", "parse.js"], "names": [], "mappings": ";;;;;;;AAAA;AACA;AACA;AACA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA,ACHA;AFOA,ACHA,ACHA;AFOA,ACHA,ACHA;AFOA,AGTA,AFMA,ACHA;AFOA,AGTA,AFMA,ACHA;AFOA,AGTA,AFMA,ACHA;AFOA,AGTA,AFMA,ACHA;AFOA,AGTA,AFMA,ACHA;AFOA,AGTA,AFMA,ACHA;AFOA,AGTA,AFMA,ACHA;ACFA,AFMA,ACHA;ACFA,AFMA,ACHA;ACFA,AFMA,ACHA;ACFA,AFMA,ACHA;ACFA,AFMA,ACHA;ACFA,AFMA,ACHA;ACFA,AFMA,ACHA;ACFA,AFMA,ACHA;ACFA,AFMA,ACHA;ACFA,AFMA,ACHA;ACFA,AFMA,ACHA;ACFA,AFMA,ACHA;ACFA,AFMA,ACHA;ACFA,AFMA,ACHA;ACFA,AFMA,ACHA;ACFA,AFMA,ACHA;ACFA,AFMA,ACHA;ACFA,AFMA,ACHA;ACFA,AFMA,ACHA;ACFA,AFMA,ACHA;ACFA,AFMA,ACHA;ACFA,AFMA,ACHA;ACFA,AFMA,ACHA;ACFA,AFMA,ACHA;ACFA,AFMA,ACHA;ACFA,AFMA,ACHA;ACFA,AFMA,ACHA;ACFA,AFMA,ACHA;ACFA,AFMA,ACHA;ACFA,AFMA,ACHA;ACFA,AFMA,ACHA;ACFA,AFMA,ACHA;ACFA,AFMA,ACHA;ACFA,AFMA,ACHA;ACFA,AFMA,ACHA;ACFA,AFMA,ACHA;ACFA,AFMA,ACHA;ACFA,AFMA,ACHA;ACFA,AFMA,ACHA;ACFA,AFMA,ACHA;ACFA,AFMA,ACHA;ACFA,AFMA,ACHA;ACFA,AFMA,ACHA;ACFA,AFMA,ACHA;ACFA,AFMA,ACHA;ACFA,AFMA,ACHA;ACFA,AFMA,ACHA;ACFA,AFMA,ACHA;ACFA,AFMA,ACHA;ACFA,AFMA,ACHA;ACFA,AFMA,ACHA;ACFA,AFMA,ACHA;ACFA,AFMA,ACHA;ACFA,AFMA,ACHA;ACFA,AFMA,ACHA;ACFA,AFMA,ACHA;ACFA,AFMA,ACHA;ACFA,AFMA,ACHA;ACFA,AFMA,ACHA;ACFA,AFMA,ACHA;ACFA,AFMA,ACHA;ACFA,AFMA,ACHA;ACFA,AFMA,ACHA;ACFA,AFMA,ACHA;ACFA,AFMA,ACHA;ACFA,AFMA,ACHA;ACFA,AFMA,ACHA;ACFA,AFMA,ACHA;ACFA,AFMA,ACHA;ACFA,AFMA,ACHA;ACFA,AFMA,ACHA;ACFA,AFMA,ACHA;ACFA,AFMA,ACHA;ACFA,AFMA,ACHA;ACFA,AFMA,ACHA;ACFA,AFMA,ACHA;ACFA,AFMA,ACHA;ACFA,AFMA,ACHA;ACFA,AFMA,ACHA;ACFA,AFMA,ACHA;ACFA,AFMA,ACHA;ACFA,AFMA,ACHA;ACFA,AFMA,ACHA;ACFA,AFMA,ACHA;ACFA,AFMA,ACHA;ACFA,AFMA,ACHA;ACFA,AFMA,ACHA;ACFA,AFMA,ACHA;ACFA,AFMA,ACHA;ACFA,AFMA,ACHA;ACFA,AFMA,ACHA;ACFA,AFMA,ACHA;ACFA,AFMA,ACHA;ACFA,AFMA,ACHA;ACFA,AFMA,ACHA;ACFA,AFMA,ACHA;ACFA,AFMA,ACHA;ACFA,AFMA,ACHA;ACFA,AFMA,ACHA;ACFA,AFMA,ACHA;ACFA,AFMA,ACHA;ACFA,AFMA,ACHA;ACFA,AFMA,ACHA;ACFA,AFMA,ACHA;ACFA,AFMA,ACHA;ACFA,AFMA,ACHA;ACFA,AFMA,ACHA;ACFA,AFMA,ACHA;ACFA,AFMA,ACHA;ACFA,AFMA,ACHA;ACFA,AFMA,ACHA;ACFA,AFMA,ACHA;ACFA,AFMA,ACHA;ACFA,AFMA,ACHA;ACFA,AFMA,ACHA;ACFA,AFMA,ACHA;ACFA,AFMA,ACHA;ACFA,AFMA,ACHA;ACFA,AFMA,ACHA;ACFA,AFMA,ACHA;ACFA,AFMA,ACHA;ACFA,AFMA,ACHA;ACFA,AFMA,ACHA;ACFA,AFMA,ACHA;ACFA,AFMA,ACHA;ACFA,AFMA,ACHA;ACFA,AFMA,ACHA;ACFA,AFMA,ACHA;ACFA,AFMA,ACHA;ACFA,AFMA,ACHA;ACFA,AFMA,ACHA;ACFA,AFMA,ACHA;ACFA,AFMA,ACHA;ACFA,AFMA,ACHA;ACFA,AFMA,ACHA;ACFA,AFMA,ACHA;ACFA,AFMA,ACHA;ACFA,AFMA,ACHA;ACFA,AFMA,ACHA;ACFA,AFMA,ACHA;ACFA,AFMA,ACHA;ACFA,AFMA,ACHA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;AACA;AACA;AACA;AACA;AACA", "file": "index.js", "sourcesContent": ["// Load modules\n\nvar Stringify = require('./stringify');\nvar Parse = require('./parse');\n\n\n// Declare internals\n\nvar internals = {};\n\n\nmodule.exports = {\n    stringify: Stringify,\n    parse: Parse\n};\n", "// Load modules\n\nvar Utils = require('./utils');\n\n\n// Declare internals\n\nvar internals = {\n    delimiter: '&',\n    arrayPrefixGenerators: {\n        brackets: function (prefix, key) {\n\n            return prefix + '[]';\n        },\n        indices: function (prefix, key) {\n\n            return prefix + '[' + key + ']';\n        },\n        repeat: function (prefix, key) {\n\n            return prefix;\n        }\n    },\n    strictNullHandling: false,\n    skipNulls: false,\n    encode: true\n};\n\n\ninternals.stringify = function (obj, prefix, generateArrayPrefix, strictNullHandling, skipNulls, encode, filter, sort) {\n\n    if (typeof filter === 'function') {\n        obj = filter(prefix, obj);\n    }\n    else if (Utils.isBuffer(obj)) {\n        obj = obj.toString();\n    }\n    else if (obj instanceof Date) {\n        obj = obj.toISOString();\n    }\n    else if (obj === null) {\n        if (strictNullHandling) {\n            return encode ? Utils.encode(prefix) : prefix;\n        }\n\n        obj = '';\n    }\n\n    if (typeof obj === 'string' ||\n        typeof obj === 'number' ||\n        typeof obj === 'boolean') {\n\n        if (encode) {\n            return [Utils.encode(prefix) + '=' + Utils.encode(obj)];\n        }\n        return [prefix + '=' + obj];\n    }\n\n    var values = [];\n\n    if (typeof obj === 'undefined') {\n        return values;\n    }\n\n    var objKeys;\n    if (Array.isArray(filter)) {\n        objKeys = filter;\n    } else {\n        var keys = Object.keys(obj);\n        objKeys = sort ? keys.sort(sort) : keys;\n    }\n\n    for (var i = 0, il = objKeys.length; i < il; ++i) {\n        var key = objKeys[i];\n\n        if (skipNulls &&\n            obj[key] === null) {\n\n            continue;\n        }\n\n        if (Array.isArray(obj)) {\n            values = values.concat(internals.stringify(obj[key], generateArrayPrefix(prefix, key), generateArrayPrefix, strictNullHandling, skipNulls, encode, filter));\n        }\n        else {\n            values = values.concat(internals.stringify(obj[key], prefix + '[' + key + ']', generateArrayPrefix, strictNullHandling, skipNulls, encode, filter));\n        }\n    }\n\n    return values;\n};\n\n\nmodule.exports = function (obj, options) {\n\n    options = options || {};\n    var delimiter = typeof options.delimiter === 'undefined' ? internals.delimiter : options.delimiter;\n    var strictNullHandling = typeof options.strictNullHandling === 'boolean' ? options.strictNullHandling : internals.strictNullHandling;\n    var skipNulls = typeof options.skipNulls === 'boolean' ? options.skipNulls : internals.skipNulls;\n    var encode = typeof options.encode === 'boolean' ? options.encode : internals.encode;\n    var sort = typeof options.sort === 'function' ? options.sort : null;\n    var objKeys;\n    var filter;\n    if (typeof options.filter === 'function') {\n        filter = options.filter;\n        obj = filter('', obj);\n    }\n    else if (Array.isArray(options.filter)) {\n        objKeys = filter = options.filter;\n    }\n\n    var keys = [];\n\n    if (typeof obj !== 'object' ||\n        obj === null) {\n\n        return '';\n    }\n\n    var arrayFormat;\n    if (options.arrayFormat in internals.arrayPrefixGenerators) {\n        arrayFormat = options.arrayFormat;\n    }\n    else if ('indices' in options) {\n        arrayFormat = options.indices ? 'indices' : 'repeat';\n    }\n    else {\n        arrayFormat = 'indices';\n    }\n\n    var generateArrayPrefix = internals.arrayPrefixGenerators[arrayFormat];\n\n    if (!objKeys) {\n        objKeys = Object.keys(obj);\n    }\n\n    if (sort) {\n        objKeys.sort(sort);\n    }\n\n    for (var i = 0, il = objKeys.length; i < il; ++i) {\n        var key = objKeys[i];\n\n        if (skipNulls &&\n            obj[key] === null) {\n\n            continue;\n        }\n\n        keys = keys.concat(internals.stringify(obj[key], key, generateArrayPrefix, strictNullHandling, skipNulls, encode, filter, sort));\n    }\n\n    return keys.join(delimiter);\n};\n", "// Load modules\n\n\n// Declare internals\n\nvar internals = {};\ninternals.hexTable = new Array(256);\nfor (var h = 0; h < 256; ++h) {\n    internals.hexTable[h] = '%' + ((h < 16 ? '0' : '') + h.toString(16)).toUpperCase();\n}\n\n\nexports.arrayToObject = function (source, options) {\n\n    var obj = options.plainObjects ? Object.create(null) : {};\n    for (var i = 0, il = source.length; i < il; ++i) {\n        if (typeof source[i] !== 'undefined') {\n\n            obj[i] = source[i];\n        }\n    }\n\n    return obj;\n};\n\n\nexports.merge = function (target, source, options) {\n\n    if (!source) {\n        return target;\n    }\n\n    if (typeof source !== 'object') {\n        if (Array.isArray(target)) {\n            target.push(source);\n        }\n        else if (typeof target === 'object') {\n            target[source] = true;\n        }\n        else {\n            target = [target, source];\n        }\n\n        return target;\n    }\n\n    if (typeof target !== 'object') {\n        target = [target].concat(source);\n        return target;\n    }\n\n    if (Array.isArray(target) &&\n        !Array.isArray(source)) {\n\n        target = exports.arrayToObject(target, options);\n    }\n\n    var keys = Object.keys(source);\n    for (var k = 0, kl = keys.length; k < kl; ++k) {\n        var key = keys[k];\n        var value = source[key];\n\n        if (!Object.prototype.hasOwnProperty.call(target, key)) {\n            target[key] = value;\n        }\n        else {\n            target[key] = exports.merge(target[key], value, options);\n        }\n    }\n\n    return target;\n};\n\n\nexports.decode = function (str) {\n\n    try {\n        return decodeURIComponent(str.replace(/\\+/g, ' '));\n    } catch (e) {\n        return str;\n    }\n};\n\nexports.encode = function (str) {\n\n    // This code was originally written by Brian White (mscdex) for the io.js core querystring library.\n    // It has been adapted here for stricter adherence to RFC 3986\n    if (str.length === 0) {\n        return str;\n    }\n\n    if (typeof str !== 'string') {\n        str = '' + str;\n    }\n\n    var out = '';\n    for (var i = 0, il = str.length; i < il; ++i) {\n        var c = str.charCodeAt(i);\n\n        if (c === 0x2D || // -\n            c === 0x2E || // .\n            c === 0x5F || // _\n            c === 0x7E || // ~\n            (c >= 0x30 && c <= 0x39) || // 0-9\n            (c >= 0x41 && c <= 0x5A) || // a-z\n            (c >= 0x61 && c <= 0x7A)) { // A-Z\n\n            out += str[i];\n            continue;\n        }\n\n        if (c < 0x80) {\n            out += internals.hexTable[c];\n            continue;\n        }\n\n        if (c < 0x800) {\n            out += internals.hexTable[0xC0 | (c >> 6)] + internals.hexTable[0x80 | (c & 0x3F)];\n            continue;\n        }\n\n        if (c < 0xD800 || c >= 0xE000) {\n            out += internals.hexTable[0xE0 | (c >> 12)] + internals.hexTable[0x80 | ((c >> 6) & 0x3F)] + internals.hexTable[0x80 | (c & 0x3F)];\n            continue;\n        }\n\n        ++i;\n        c = 0x10000 + (((c & 0x3FF) << 10) | (str.charCodeAt(i) & 0x3FF));\n        out += internals.hexTable[0xF0 | (c >> 18)] + internals.hexTable[0x80 | ((c >> 12) & 0x3F)] + internals.hexTable[0x80 | ((c >> 6) & 0x3F)] + internals.hexTable[0x80 | (c & 0x3F)];\n    }\n\n    return out;\n};\n\nexports.compact = function (obj, refs) {\n\n    if (typeof obj !== 'object' ||\n        obj === null) {\n\n        return obj;\n    }\n\n    refs = refs || [];\n    var lookup = refs.indexOf(obj);\n    if (lookup !== -1) {\n        return refs[lookup];\n    }\n\n    refs.push(obj);\n\n    if (Array.isArray(obj)) {\n        var compacted = [];\n\n        for (var i = 0, il = obj.length; i < il; ++i) {\n            if (typeof obj[i] !== 'undefined') {\n                compacted.push(obj[i]);\n            }\n        }\n\n        return compacted;\n    }\n\n    var keys = Object.keys(obj);\n    for (i = 0, il = keys.length; i < il; ++i) {\n        var key = keys[i];\n        obj[key] = exports.compact(obj[key], refs);\n    }\n\n    return obj;\n};\n\n\nexports.isRegExp = function (obj) {\n\n    return Object.prototype.toString.call(obj) === '[object RegExp]';\n};\n\n\nexports.isBuffer = function (obj) {\n\n    if (obj === null ||\n        typeof obj === 'undefined') {\n\n        return false;\n    }\n\n    return !!(obj.constructor &&\n              obj.constructor.isBuffer &&\n              obj.constructor.isBuffer(obj));\n};\n", "// Load modules\n\nvar Utils = require('./utils');\n\n\n// Declare internals\n\nvar internals = {\n    delimiter: '&',\n    depth: 5,\n    arrayLimit: 20,\n    parameterLimit: 1000,\n    strictNullHandling: false,\n    plainObjects: false,\n    allowPrototypes: false,\n    allowDots: false\n};\n\n\ninternals.parseValues = function (str, options) {\n\n    var obj = {};\n    var parts = str.split(options.delimiter, options.parameterLimit === Infinity ? undefined : options.parameterLimit);\n\n    for (var i = 0, il = parts.length; i < il; ++i) {\n        var part = parts[i];\n        var pos = part.indexOf(']=') === -1 ? part.indexOf('=') : part.indexOf(']=') + 1;\n\n        var key, val;\n        if (pos === -1) {\n            key = Utils.decode(part);\n            val = options.strictNullHandling ? null : '';\n        } else {\n            key = Utils.decode(part.slice(0, pos));\n            val = Utils.decode(part.slice(pos + 1));\n        }\n        if (Object.prototype.hasOwnProperty.call(obj, key)) {\n            obj[key] = [].concat(obj[key]).concat(val);\n        } else {\n            obj[key] = val;\n        }\n    }\n\n    return obj;\n};\n\n\ninternals.parseObject = function (chain, val, options) {\n\n    if (!chain.length) {\n        return val;\n    }\n\n    var root = chain.shift();\n\n    var obj;\n    if (root === '[]') {\n        obj = [];\n        obj = obj.concat(internals.parseObject(chain, val, options));\n    }\n    else {\n        obj = options.plainObjects ? Object.create(null) : {};\n        var cleanRoot = root[0] === '[' && root[root.length - 1] === ']' ? root.slice(1, root.length - 1) : root;\n        var index = parseInt(cleanRoot, 10);\n        var indexString = '' + index;\n        if (!isNaN(index) &&\n            root !== cleanRoot &&\n            indexString === cleanRoot &&\n            index >= 0 &&\n            (options.parseArrays &&\n             index <= options.arrayLimit)) {\n\n            obj = [];\n            obj[index] = internals.parseObject(chain, val, options);\n        }\n        else {\n            obj[cleanRoot] = internals.parseObject(chain, val, options);\n        }\n    }\n\n    return obj;\n};\n\n\ninternals.parseKeys = function (key, val, options) {\n\n    if (!key) {\n        return;\n    }\n\n    // Transform dot notation to bracket notation\n\n    if (options.allowDots) {\n        key = key.replace(/\\.([^\\.\\[]+)/g, '[$1]');\n    }\n\n    // The regex chunks\n\n    var parent = /^([^\\[\\]]*)/;\n    var child = /(\\[[^\\[\\]]*\\])/g;\n\n    // Get the parent\n\n    var segment = parent.exec(key);\n\n    // Stash the parent if it exists\n\n    var keys = [];\n    if (segment[1]) {\n        // If we aren't using plain objects, optionally prefix keys\n        // that would overwrite object prototype properties\n        if (!options.plainObjects &&\n            Object.prototype.hasOwnProperty(segment[1])) {\n\n            if (!options.allowPrototypes) {\n                return;\n            }\n        }\n\n        keys.push(segment[1]);\n    }\n\n    // Loop through children appending to the array until we hit depth\n\n    var i = 0;\n    while ((segment = child.exec(key)) !== null && i < options.depth) {\n\n        ++i;\n        if (!options.plainObjects &&\n            Object.prototype.hasOwnProperty(segment[1].replace(/\\[|\\]/g, ''))) {\n\n            if (!options.allowPrototypes) {\n                continue;\n            }\n        }\n        keys.push(segment[1]);\n    }\n\n    // If there's a remainder, just add whatever is left\n\n    if (segment) {\n        keys.push('[' + key.slice(segment.index) + ']');\n    }\n\n    return internals.parseObject(keys, val, options);\n};\n\n\nmodule.exports = function (str, options) {\n\n    options = options || {};\n    options.delimiter = typeof options.delimiter === 'string' || Utils.isRegExp(options.delimiter) ? options.delimiter : internals.delimiter;\n    options.depth = typeof options.depth === 'number' ? options.depth : internals.depth;\n    options.arrayLimit = typeof options.arrayLimit === 'number' ? options.arrayLimit : internals.arrayLimit;\n    options.parseArrays = options.parseArrays !== false;\n    options.allowDots = typeof options.allowDots === 'boolean' ? options.allowDots : internals.allowDots;\n    options.plainObjects = typeof options.plainObjects === 'boolean' ? options.plainObjects : internals.plainObjects;\n    options.allowPrototypes = typeof options.allowPrototypes === 'boolean' ? options.allowPrototypes : internals.allowPrototypes;\n    options.parameterLimit = typeof options.parameterLimit === 'number' ? options.parameterLimit : internals.parameterLimit;\n    options.strictNullHandling = typeof options.strictNullHandling === 'boolean' ? options.strictNullHandling : internals.strictNullHandling;\n\n    if (str === '' ||\n        str === null ||\n        typeof str === 'undefined') {\n\n        return options.plainObjects ? Object.create(null) : {};\n    }\n\n    var tempObj = typeof str === 'string' ? internals.parseValues(str, options) : str;\n    var obj = options.plainObjects ? Object.create(null) : {};\n\n    // Iterate over the keys and setup the new object\n\n    var keys = Object.keys(tempObj);\n    for (var i = 0, il = keys.length; i < il; ++i) {\n        var key = keys[i];\n        var newObj = internals.parseKeys(key, tempObj[key], options);\n        obj = Utils.merge(obj, newObj, options);\n    }\n\n    return Utils.compact(obj);\n};\n"]}