<!-- pages/orders/index.wxml -->
<view class="container">
  <view class="header">
    <text class="title">我的订单</text>
  </view>
  
  <view class="content">
    <!-- 订单列表将在这里显示 -->
    <view class="order-list">
      <view class="order-item" wx:for="{{orders}}" wx:key="id">
        <view class="order-header">
          <text class="order-id">订单号: {{item.orderId}}</text>
          <text class="order-status">{{item.status}}</text>
        </view>
        <view class="order-details">
          <view class="product-info">
            <image class="product-image" src="{{item.productImage}}" mode="aspectFill"></image>
            <view class="product-text">
              <text class="product-name">{{item.productName}}</text>
              <text class="product-price">¥{{item.price}}</text>
            </view>
          </view>
          <view class="order-summary">
            <text class="quantity">数量: {{item.quantity}}</text>
            <text class="total">总计: ¥{{item.total}}</text>
          </view>
        </view>
      </view>
      
      <!-- 如果没有订单，显示提示信息 -->
      <view class="no-orders" wx:if="{{orders.length === 0}}">
        <text class="no-orders-text">暂无订单</text>
      </view>
    </view>
  </view>
</view>