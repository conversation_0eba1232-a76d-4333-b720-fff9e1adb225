/* pages/orders/index.wxss */
.container {
  min-height: 100vh;
  background-color: var(--bg-primary);
  padding-bottom: calc(env(safe-area-inset-bottom) + var(--spacing-xl));
}

.header {
  background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
  padding: var(--spacing-xl) var(--spacing-lg);
  text-align: center;
  position: relative;
}

.header::after {
  content: '';
  position: absolute;
  bottom: -20rpx;
  left: 0;
  width: 100%;
  height: 40rpx;
  background: linear-gradient(to top, var(--bg-primary), transparent);
}

.title {
  color: var(--text-inverse);
  font-size: var(--font-size-xl);
  font-weight: 600;
  position: relative;
  z-index: 1;
}

.content {
  padding: var(--spacing-lg);
  margin-top: var(--spacing-md);
}

.order-list {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-md);
}

.order-item {
  background-color: var(--bg-card);
  border-radius: var(--radius-lg);
  padding: var(--spacing-lg);
  box-shadow: var(--shadow-md);
  transition: transform var(--transition-fast), box-shadow var(--transition-fast);
  position: relative;
  overflow: hidden;
}

.order-item::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 6rpx;
  height: 100%;
  background-color: var(--primary-color);
  opacity: 0;
  transition: opacity var(--transition-fast);
}

.order-item:active {
  transform: translateY(2rpx);
  box-shadow: var(--shadow-sm);
}

.order-item:active::before {
  opacity: 1;
}

.order-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-bottom: var(--spacing-md);
  border-bottom: 2rpx solid var(--border-color);
}

.order-id {
  font-size: var(--font-size-md);
  color: var(--text-primary);
  font-weight: 500;
  display: flex;
  align-items: center;
}

.order-id-icon {
  width: 32rpx;
  height: 32rpx;
  margin-right: var(--spacing-sm);
  color: var(--primary-color);
}

.order-status {
  font-size: var(--font-size-sm);
  padding: var(--spacing-xs) var(--spacing-sm);
  border-radius: var(--radius-full);
  font-weight: 500;
}

.status-pending {
  background-color: rgba(255, 149, 0, 0.1);
  color: var(--warning-color);
}

.status-processing {
  background-color: rgba(0, 102, 204, 0.1);
  color: var(--primary-color);
}

.status-completed {
  background-color: rgba(52, 199, 89, 0.1);
  color: var(--success-color);
}

.status-cancelled {
  background-color: rgba(255, 59, 48, 0.1);
  color: var(--error-color);
}

.order-details {
  padding-top: var(--spacing-md);
}

.product-info {
  display: flex;
  align-items: center;
  margin-bottom: var(--spacing-md);
}

.product-image {
  width: 120rpx;
  height: 120rpx;
  border-radius: var(--radius-md);
  margin-right: var(--spacing-md);
  box-shadow: var(--shadow-sm);
}

.product-text {
  display: flex;
  flex-direction: column;
  flex: 1;
}

.product-name {
  font-size: var(--font-size-md);
  color: var(--text-primary);
  margin-bottom: var(--spacing-xs);
  font-weight: 500;
  line-height: var(--line-height-md);
}

.product-desc {
  font-size: var(--font-size-sm);
  color: var(--text-tertiary);
  margin-bottom: var(--spacing-xs);
}

.product-price {
  font-size: var(--font-size-lg);
  color: var(--error-color);
  font-weight: 600;
}

.order-summary {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: var(--spacing-md);
  padding-top: var(--spacing-md);
  border-top: 2rpx solid var(--border-color);
}

.quantity {
  font-size: var(--font-size-sm);
  color: var(--text-tertiary);
  display: flex;
  align-items: center;
}

.quantity-icon {
  width: 24rpx;
  height: 24rpx;
  margin-right: var(--spacing-xs);
  color: var(--text-quaternary);
}

.total {
  font-size: var(--font-size-lg);
  color: var(--error-color);
  font-weight: 600;
  display: flex;
  align-items: center;
}

.total-label {
  font-size: var(--font-size-sm);
  color: var(--text-tertiary);
  margin-right: var(--spacing-xs);
  font-weight: normal;
}

.order-actions {
  display: flex;
  justify-content: flex-end;
  gap: var(--spacing-sm);
  margin-top: var(--spacing-md);
  padding-top: var(--spacing-md);
  border-top: 2rpx solid var(--border-color);
}

.action-button {
  padding: var(--spacing-xs) var(--spacing-md);
  border-radius: var(--radius-md);
  font-size: var(--font-size-sm);
  font-weight: 500;
  transition: all var(--transition-fast);
}

.action-button-outline {
  background-color: transparent;
  color: var(--primary-color);
  border: 2rpx solid var(--primary-color);
}

.action-button-outline:active {
  background-color: var(--primary-color);
  color: var(--text-inverse);
}

.action-button-primary {
  background-color: var(--primary-color);
  color: var(--text-inverse);
  border: none;
}

.action-button-primary:active {
  background-color: var(--primary-dark);
}

.no-orders {
  text-align: center;
  padding: var(--spacing-xl) 0;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.no-orders-icon {
  width: 200rpx;
  height: 200rpx;
  margin-bottom: var(--spacing-lg);
  opacity: 0.3;
}

.no-orders-text {
  font-size: var(--font-size-md);
  color: var(--text-quaternary);
  margin-bottom: var(--spacing-md);
}

.no-orders-button {
  padding: var(--spacing-sm) var(--spacing-lg);
  background-color: var(--primary-color);
  color: var(--text-inverse);
  border-radius: var(--radius-md);
  font-size: var(--font-size-md);
  font-weight: 500;
  box-shadow: var(--shadow-sm);
  transition: all var(--transition-fast);
}

.no-orders-button:active {
  background-color: var(--primary-dark);
  transform: translateY(2rpx);
}