// pages/booking/booking.js
Page({

  /**
   * 页面的初始数据
   */
  data: {
    selectedSubject: 'subject2', // 默认选择科目二
    selectedCarType: 'C1', // 默认选择C1
    selectedDate: '',
    selectedQuickDate: 'today', // 默认选择今天
    selectedTimeSlot: 'morning1', // 默认选择08:00-10:00
    showCalendar: false,
    minDate: new Date().getTime(),
    maxDate: new Date(2025, 11, 31).getTime(), // 2025年12月31日
    availableDates: ['2025-01-10', '2025-01-12', '2025-01-16', '2025-01-29'], // 有考试安排的日期
    quickDates: ['08-04', '08-05', '08-06', '08-07'] // 快捷日期显示
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function (options) {
    // 初始化今天的日期
    const today = new Date();
    const todayStr = `${today.getFullYear()}-${String(today.getMonth() + 1).padStart(2, '0')}-${String(today.getDate()).padStart(2, '0')}`;
    this.setData({
      selectedDate: todayStr
    });

    // 更新快捷日期显示
    this.updateQuickDates();
  },

  /**
   * 更新快捷日期显示
   */
  updateQuickDates() {
    const today = new Date();
    const dates = [];

    for (let i = 0; i < 4; i++) {
      const date = new Date(today);
      date.setDate(today.getDate() + i);
      const month = String(date.getMonth() + 1).padStart(2, '0');
      const day = String(date.getDate()).padStart(2, '0');
      dates.push(`${month}-${day}`);
    }

    this.setData({
      quickDates: dates
    });
  },

  /**
   * 选择考试科目
   */
  selectSubject(e) {
    const subject = e.currentTarget.dataset.subject;
    this.setData({
      selectedSubject: subject
    });
  },

  /**
   * 选择车型
   */
  selectCarType(e) {
    const type = e.currentTarget.dataset.type;
    this.setData({
      selectedCarType: type
    });
  },

  /**
   * 选择快捷日期
   */
  selectQuickDate(e) {
    const dateType = e.currentTarget.dataset.date;
    const today = new Date();
    let selectedDate = '';

    switch (dateType) {
      case 'today':
        selectedDate = this.formatDate(today);
        break;
      case 'tomorrow':
        const tomorrow = new Date(today);
        tomorrow.setDate(today.getDate() + 1);
        selectedDate = this.formatDate(tomorrow);
        break;
      case 'day3':
        const day3 = new Date(today);
        day3.setDate(today.getDate() + 2);
        selectedDate = this.formatDate(day3);
        break;
      case 'day4':
        const day4 = new Date(today);
        day4.setDate(today.getDate() + 3);
        selectedDate = this.formatDate(day4);
        break;
    }

    this.setData({
      selectedQuickDate: dateType,
      selectedDate: selectedDate
    });
  },

  /**
   * 选择时间段
   */
  selectTimeSlot(e) {
    const slot = e.currentTarget.dataset.slot;
    this.setData({
      selectedTimeSlot: slot
    });
  },

  /**
   * 打开日历
   */
  openCalendar() {
    wx.navigateTo({
      url: `/pages/calendar/calendar?selectedDate=${this.data.selectedDate}`
    });
  },

  /**
   * 关闭日历
   */
  closeCalendar() {
    this.setData({
      showCalendar: false
    });
  },

  /**
   * 日历确认选择
   */
  onCalendarConfirm(e) {
    const selectedDate = e.detail.date;
    this.setData({
      selectedDate: selectedDate,
      showCalendar: false,
      selectedQuickDate: '' // 清除快捷选择
    });
  },

  /**
   * 格式化日期
   */
  formatDate(date) {
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    return `${year}-${month}-${day}`;
  },

  /**
   * 提交预约
   */
  submitBooking() {
    // 验证必填项
    if (!this.data.selectedSubject) {
      wx.showToast({
        title: '请选择考试科目',
        icon: 'none'
      });
      return;
    }

    if (!this.data.selectedCarType) {
      wx.showToast({
        title: '请选择车型',
        icon: 'none'
      });
      return;
    }

    if (!this.data.selectedDate) {
      wx.showToast({
        title: '请选择考试日期',
        icon: 'none'
      });
      return;
    }

    if (!this.data.selectedTimeSlot) {
      wx.showToast({
        title: '请选择时间段',
        icon: 'none'
      });
      return;
    }

    // 构建预约数据
    const bookingData = {
      subject: this.data.selectedSubject,
      carType: this.data.selectedCarType,
      date: this.data.selectedDate,
      timeSlot: this.data.selectedTimeSlot,
      examCount: 1,
      price: 150
    };

    // 跳转到确认页面
    wx.navigateTo({
      url: `/pages/booking-confirm/booking-confirm?data=${encodeURIComponent(JSON.stringify(bookingData))}`
    });
  },

  /**
   * 返回上一页
   */
  goBack() {
    wx.navigateBack();
  },

  /**
   * 回到首页
   */
  goHome() {
    wx.switchTab({
      url: '/pages/index/index'
    });
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady: function () {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow: function () {

  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide: function () {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload: function () {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh: function () {

  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom: function () {

  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage: function () {

  }
})
