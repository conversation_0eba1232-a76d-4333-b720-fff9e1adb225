<!--pages/booking/booking.wxml-->
<view class="container">
  <!-- 顶部导航栏 -->
  <view class="top-navbar">
    <van-icon name="arrow-left" size="20px" color="#fff" bindtap="goBack" />
    <text class="navbar-title">模拟考试预约</text>
    <view class="navbar-right">
      <van-icon name="ellipsis" size="20px" color="#fff" />
      <van-icon name="eye-o" size="20px" color="#fff" />
    </view>
  </view>

  <!-- 主要内容区域 -->
  <view class="main-content">
    <!-- 考试科目 -->
    <view class="section">
      <text class="section-title">考试科目</text>
      <view class="option-group">
        <view class="option-item {{selectedSubject === 'subject2' ? 'selected' : ''}}" 
              bindtap="selectSubject" data-subject="subject2">
          <text class="option-text">科目二</text>
        </view>
        <view class="option-item {{selectedSubject === 'subject3' ? 'selected' : ''}}" 
              bindtap="selectSubject" data-subject="subject3">
          <text class="option-text">科目三</text>
        </view>
      </view>
    </view>

    <!-- 车型选择 -->
    <view class="section">
      <text class="section-title">车型选择</text>
      <view class="option-group">
        <view class="option-item {{selectedCarType === 'C1' ? 'selected' : ''}}" 
              bindtap="selectCarType" data-type="C1">
          <text class="option-text">小型汽车(C1)</text>
        </view>
        <view class="option-item {{selectedCarType === 'C2' ? 'selected' : ''}}" 
              bindtap="selectCarType" data-type="C2">
          <text class="option-text">小型手动挡汽车(C2)</text>
        </view>
      </view>
    </view>

    <!-- 选择日期 -->
    <view class="section">
      <text class="section-title">选择日期</text>
      <view class="date-selector" bindtap="openCalendar">
        <view class="date-display">
          <text class="date-text">{{selectedDate || '请选择日期'}}</text>
          <van-icon name="calendar-o" size="20px" color="#4A90E2" />
        </view>
      </view>
      
      <!-- 日期快捷选择 -->
      <view class="date-quick-select">
        <view class="quick-date-item {{selectedQuickDate === 'today' ? 'selected' : ''}}" 
              bindtap="selectQuickDate" data-date="today">
          <text class="quick-date-text">今天</text>
          <text class="quick-date-detail">08-04</text>
        </view>
        <view class="quick-date-item {{selectedQuickDate === 'tomorrow' ? 'selected' : ''}}" 
              bindtap="selectQuickDate" data-date="tomorrow">
          <text class="quick-date-text">明天</text>
          <text class="quick-date-detail">08-05</text>
        </view>
        <view class="quick-date-item {{selectedQuickDate === 'day3' ? 'selected' : ''}}" 
              bindtap="selectQuickDate" data-date="day3">
          <text class="quick-date-text">周三</text>
          <text class="quick-date-detail">08-06</text>
        </view>
        <view class="quick-date-item {{selectedQuickDate === 'day4' ? 'selected' : ''}}" 
              bindtap="selectQuickDate" data-date="day4">
          <text class="quick-date-text">周四</text>
          <text class="quick-date-detail">08-07</text>
        </view>
      </view>
    </view>

    <!-- 时间段 -->
    <view class="section">
      <text class="section-title">时间段</text>
      <view class="time-slots">
        <view class="time-slot {{selectedTimeSlot === 'morning1' ? 'selected' : ''}}" 
              bindtap="selectTimeSlot" data-slot="morning1">
          <text class="time-text">08:00-10:00</text>
        </view>
        <view class="time-slot {{selectedTimeSlot === 'morning2' ? 'selected' : ''}}" 
              bindtap="selectTimeSlot" data-slot="morning2">
          <text class="time-text">10:00-12:00</text>
        </view>
        <view class="time-slot {{selectedTimeSlot === 'afternoon1' ? 'selected' : ''}}" 
              bindtap="selectTimeSlot" data-slot="afternoon1">
          <text class="time-text">14:00-16:00</text>
        </view>
        <view class="time-slot {{selectedTimeSlot === 'afternoon2' ? 'selected' : ''}}" 
              bindtap="selectTimeSlot" data-slot="afternoon2">
          <text class="time-text">16:00-18:00</text>
        </view>
      </view>
    </view>

    <!-- 考试条数 -->
    <view class="section">
      <text class="section-title">考试条数</text>
      <view class="exam-count-selector">
        <text class="count-text">1条</text>
        <text class="price-text">150元</text>
      </view>
    </view>

    <!-- 立即预约按钮 -->
    <view class="booking-button-container">
      <view class="booking-button" bindtap="submitBooking">
        <text class="button-text">立即预约</text>
      </view>
    </view>
  </view>

  <!-- 日历弹窗 -->
  <van-popup show="{{showCalendar}}" position="bottom" bind:close="closeCalendar">
    <view class="calendar-popup">
      <view class="calendar-header">
        <text class="calendar-title">选择日期</text>
        <van-icon name="cross" size="20px" bindtap="closeCalendar" />
      </view>
      <custom-calendar
        selected-date="{{selectedDate}}"
        bind:dateSelect="onCalendarConfirm"
        event-dates="{{availableDates}}"
      />
    </view>
  </van-popup>

  <!-- 底部导航栏 -->
  <view class="bottom-tabbar">
    <view class="tab-item" bindtap="goHome">
      <van-icon name="home-o" size="24px" color="#999" />
      <text class="tab-text">首页</text>
    </view>
    <view class="tab-item">
      <van-icon name="user-o" size="24px" color="#999" />
      <text class="tab-text">我的</text>
    </view>
  </view>
</view>
