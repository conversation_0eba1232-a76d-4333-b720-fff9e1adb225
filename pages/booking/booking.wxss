/* pages/booking/booking.wxss */
page {
  background-color: #f5f5f5;
  height: 100vh;
}

.container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background-color: #f5f5f5;
}

/* 顶部导航栏 */
.top-navbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: calc(env(safe-area-inset-top) + 10px) 20px 15px 20px;
  background: linear-gradient(135deg, #4A90E2 0%, #357ABD 100%);
}

.navbar-title {
  font-size: 18px;
  font-weight: 600;
  color: #fff;
}

.navbar-right {
  display: flex;
  align-items: center;
  gap: 15px;
}

/* 主要内容区域 */
.main-content {
  flex: 1;
  background-color: #f5f5f5;
  padding: 20px 15px;
  overflow-y: auto;
}

/* 区块样式 */
.section {
  margin-bottom: 25px;
}

.section-title {
  font-size: 15px;
  font-weight: 600;
  color: #333;
  margin-bottom: 12px;
  display: block;
  padding-left: 2px;
}

/* 选项组 */
.option-group {
  display: flex;
  gap: 12px;
}

.option-item {
  flex: 1;
  padding: 12px 8px;
  background-color: #fff;
  border-radius: 8px;
  text-align: center;
  border: 1px solid #e5e5e5;
  transition: all 0.2s ease;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

.option-item.selected {
  border-color: #4A90E2;
  background-color: #f0f8ff;
}

.option-text {
  font-size: 13px;
  color: #333;
  font-weight: 500;
}

/* 日期选择器 */
.date-selector {
  background-color: #fff;
  border-radius: 8px;
  padding: 12px 15px;
  margin-bottom: 12px;
  border: 1px solid #e5e5e5;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

.date-display {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.date-text {
  font-size: 13px;
  color: #333;
}

/* 日期快捷选择 */
.date-quick-select {
  display: flex;
  gap: 8px;
}

.quick-date-item {
  flex: 1;
  padding: 10px 6px;
  background-color: #fff;
  border-radius: 6px;
  text-align: center;
  border: 1px solid #e5e5e5;
  transition: all 0.2s ease;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

.quick-date-item.selected {
  border-color: #4A90E2;
  background-color: #f0f8ff;
}

.quick-date-text {
  display: block;
  font-size: 11px;
  color: #333;
  font-weight: 500;
  margin-bottom: 2px;
}

.quick-date-detail {
  display: block;
  font-size: 10px;
  color: #666;
}

/* 时间段 */
.time-slots {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.time-slot {
  flex: 0 0 calc(50% - 4px);
  padding: 12px 8px;
  background-color: #fff;
  border-radius: 8px;
  text-align: center;
  border: 1px solid #e5e5e5;
  transition: all 0.2s ease;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

.time-slot.selected {
  border-color: #4A90E2;
  background-color: #f0f8ff;
}

.time-text {
  font-size: 13px;
  color: #333;
  font-weight: 500;
}

/* 考试条数选择器 */
.exam-count-selector {
  background-color: #fff;
  border-radius: 8px;
  padding: 12px 15px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border: 1px solid #4A90E2;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

.count-text {
  font-size: 14px;
  color: #4A90E2;
  font-weight: 600;
}

.price-text {
  font-size: 14px;
  color: #4A90E2;
  font-weight: 600;
}

/* 预约按钮 */
.booking-button-container {
  margin-top: 25px;
  margin-bottom: 70px;
  padding: 0 5px;
}

.booking-button {
  width: 100%;
  height: 44px;
  background: linear-gradient(135deg, #4A90E2 0%, #357ABD 100%);
  border-radius: 22px;
  display: flex;
  justify-content: center;
  align-items: center;
  box-shadow: 0 2px 8px rgba(74, 144, 226, 0.3);
}

.button-text {
  font-size: 15px;
  font-weight: 600;
  color: #fff;
}

/* 日历弹窗 */
.calendar-popup {
  background-color: #fff;
  border-radius: 20px 20px 0 0;
  max-height: 80vh;
}

.calendar-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px;
  border-bottom: 1px solid #eee;
}

.calendar-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
}

/* 底部导航栏 */
.bottom-tabbar {
  display: flex;
  background-color: #fff;
  border-top: 1px solid #eee;
  padding: 5px 0;
  padding-bottom: calc(5px + env(safe-area-inset-bottom));
}

.tab-item {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 5px 0;
  transition: all 0.2s ease;
}

.tab-text {
  font-size: 10px;
  color: #999;
  margin-top: 2px;
}
