// pages/suggestion/suggestion.js
Page({
    /**
     * 页面的初始数据
     */
    data: {
        name: '',        // 姓名
        contact: '',     // 联系方式
        title: '',       // 主题
        content: ''      // 内容
    },

    /**
     * 生命周期函数--监听页面加载
     */
    onLoad: function (options) {
        // 页面加载时的初始化操作
    },

    /**
     * 生命周期函数--监听页面显示
     */
    onShow: function () {
        // 页面显示时的操作
    },

    /**
     * 表单字段变化处理函数
     */
    onFieldChange: function (e) {
        const { name } = e.currentTarget.dataset;
        const { value } = e.detail;

        this.setData({
            [name]: value
        });
    },

    /**
     * 表单提交处理函数
     */
    onSubmit: function (e) {
        // 获取表单数据
        const { name, contact, title, content } = this.data;

        // 表单验证
        if (!this.validateForm()) {
            return;
        }

        // 模拟提交数据到服务器
        this.submitSuggestion({ name, contact, title, content });
    },

    /**
     * 表单验证函数
     */
    validateForm: function () {
        const { name, contact, title, content } = this.data;

        // 验证姓名
        if (!name || name.trim() === '') {
            wx.showToast({
                title: '请输入姓名',
                icon: 'none'
            });
            return false;
        }

        // 验证联系方式
        if (!contact || contact.trim() === '') {
            wx.showToast({
                title: '请输入联系方式',
                icon: 'none'
            });
            return false;
        }

        // 简单的手机号或邮箱验证
        const phoneRegex = /^1[3-9]\d{9}$/;
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        if (!phoneRegex.test(contact) && !emailRegex.test(contact)) {
            wx.showToast({
                title: '请输入正确的手机号或邮箱',
                icon: 'none'
            });
            return false;
        }

        // 验证主题
        if (!title || title.trim() === '') {
            wx.showToast({
                title: '请输入主题',
                icon: 'none'
            });
            return false;
        }

        // 验证内容
        if (!content || content.trim() === '') {
            wx.showToast({
                title: '请输入内容',
                icon: 'none'
            });
            return false;
        }

        // 内容长度验证
        if (content.length < 10) {
            wx.showToast({
                title: '内容至少10个字符',
                icon: 'none'
            });
            return false;
        }

        return true;
    },

    /**
     * 提交投诉建议到服务器
     */
    submitSuggestion: function (suggestionData) {
        // 显示加载提示
        wx.showLoading({
            title: '提交中...'
        });

        // 模拟网络请求（实际开发中需要替换为真实的API调用）
        setTimeout(() => {
            // 隐藏加载提示
            wx.hideLoading();

            // 显示提交成功的提示
            wx.showToast({
                title: '提交成功',
                icon: 'success'
            });

            // 重置表单
            this.setData({
                name: '',
                contact: '',
                title: '',
                content: ''
            });

            // 可以根据需求决定是否返回上一页
            // wx.navigateBack();
        }, 1500);

        // 实际开发中的网络请求示例：
        /*
        wx.request({
          url: 'https://your-api-endpoint.com/suggestions',
          method: 'POST',
          data: suggestionData,
          success: (res) => {
            wx.hideLoading();
            if (res.statusCode === 200) {
              wx.showToast({
                title: '提交成功',
                icon: 'success'
              });
              // 重置表单
              this.setData({
                name: '',
                contact: '',
                title: '',
                content: ''
              });
            } else {
              wx.showToast({
                title: '提交失败，请重试',
                icon: 'none'
              });
            }
          },
          fail: (err) => {
            wx.hideLoading();
            wx.showToast({
              title: '网络错误，请重试',
              icon: 'none'
            });
            console.error('提交失败', err);
          }
        });
        */
    },

    /**
     * 生命周期函数--监听页面初次渲染完成
     */
    onReady: function () {
        // 页面初次渲染完成时的操作
    },

    /**
     * 生命周期函数--监听页面隐藏
     */
    onHide: function () {
        // 页面隐藏时的操作
    },

    /**
     * 生命周期函数--监听页面卸载
     */
    onUnload: function () {
        // 页面卸载时的操作
    },

    /**
     * 页面相关事件处理函数--监听用户下拉动作
     */
    onPullDownRefresh: function () {
        // 下拉刷新时的操作
    },

    /**
     * 页面上拉触底事件的处理函数
     */
    onReachBottom: function () {
        // 上拉触底时的操作
    },

    /**
     * 用户点击右上角分享
     */
    onShareAppMessage: function () {
        // 分享功能
        return {
            title: '投诉建议',
            path: '/pages/suggestion/suggestion'
        };
    }
})