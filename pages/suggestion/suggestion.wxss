/* 投诉建议页面样式 */
.suggestion-container {
  padding: var(--spacing-lg);
  background-color: var(--bg-primary);
  min-height: 100vh;
  padding-bottom: calc(env(safe-area-inset-bottom) + var(--spacing-xl));
}

.suggestion-header {
  background-color: var(--bg-card);
  border-radius: var(--radius-lg);
  padding: var(--spacing-xl);
  margin-bottom: var(--spacing-md);
  box-shadow: var(--shadow-md);
  position: relative;
  overflow: hidden;
}

.suggestion-header::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 6rpx;
  background: linear-gradient(90deg, var(--secondary-color), var(--secondary-light), var(--secondary-color));
  animation: shimmer 2s infinite;
}

@keyframes shimmer {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

.header-title {
  font-size: var(--font-size-xl);
  font-weight: 600;
  color: var(--text-primary);
  display: block;
  margin-bottom: var(--spacing-sm);
  position: relative;
  z-index: 1;
}

.header-title-icon {
  width: 40rpx;
  height: 40rpx;
  margin-right: var(--spacing-sm);
  vertical-align: middle;
  color: var(--secondary-color);
}

.header-subtitle {
  font-size: var(--font-size-md);
  color: var(--text-secondary);
  display: block;
  line-height: var(--line-height-md);
}

.submit-section {
  margin-top: var(--spacing-xl);
  padding: 0 var(--spacing-lg);
}

.submit-btn {
  width: 100%;
  height: 88rpx;
  line-height: 88rpx;
  font-size: var(--font-size-md);
  font-weight: 500;
  background-color: var(--secondary-color);
  color: var(--text-inverse);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-md);
  transition: all var(--transition-fast);
  display: flex;
  align-items: center;
  justify-content: center;
}

.submit-btn:active {
  background-color: var(--secondary-dark);
  transform: translateY(2rpx);
  box-shadow: var(--shadow-sm);
}

.submit-btn-icon {
  width: 32rpx;
  height: 32rpx;
  margin-right: var(--spacing-sm);
}

/* 表单元素样式 */
.van-cell-group {
  border-radius: var(--radius-lg);
  overflow: hidden;
  box-shadow: var(--shadow-md);
  margin-bottom: var(--spacing-md);
}

.van-cell {
  background-color: var(--bg-card);
  transition: background-color var(--transition-fast);
}

.van-cell:active {
  background-color: var(--bg-tertiary);
}

.van-field__label {
  font-size: var(--font-size-md);
  color: var(--text-primary);
  font-weight: 500;
}

.van-field__input {
  font-size: var(--font-size-md);
  color: var(--text-secondary);
}

.van-field__textarea {
  font-size: var(--font-size-md);
  color: var(--text-secondary);
  min-height: 200rpx;
  line-height: var(--line-height-md);
}

/* 表单分组样式 */
.form-section {
  background-color: var(--bg-card);
  border-radius: var(--radius-lg);
  padding: var(--spacing-lg);
  margin-bottom: var(--spacing-md);
  box-shadow: var(--shadow-md);
}

.section-title {
  font-size: var(--font-size-md);
  font-weight: 500;
  color: var(--text-primary);
  margin-bottom: var(--spacing-md);
  padding-bottom: var(--spacing-sm);
  border-bottom: 2rpx solid var(--border-color);
  display: flex;
  align-items: center;
}

.section-icon {
  width: 32rpx;
  height: 32rpx;
  margin-right: var(--spacing-sm);
  color: var(--secondary-color);
}

/* 提示信息样式 */
.tips {
  background-color: rgba(52, 199, 89, 0.1);
  border-radius: var(--radius-md);
  padding: var(--spacing-md);
  margin-bottom: var(--spacing-md);
}

.tips-title {
  font-size: var(--font-size-md);
  font-weight: 500;
  color: var(--secondary-color);
  margin-bottom: var(--spacing-sm);
  display: flex;
  align-items: center;
}

.tips-icon {
  width: 32rpx;
  height: 32rpx;
  margin-right: var(--spacing-sm);
}

.tips-content {
  font-size: var(--font-size-sm);
  color: var(--text-secondary);
  line-height: var(--line-height-md);
}

/* 图片上传样式 */
.image-upload {
  display: flex;
  flex-wrap: wrap;
  gap: var(--spacing-sm);
  margin-top: var(--spacing-sm);
}

.upload-item {
  width: 160rpx;
  height: 160rpx;
  border-radius: var(--radius-md);
  background-color: var(--bg-tertiary);
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  overflow: hidden;
}

.upload-icon {
  width: 48rpx;
  height: 48rpx;
  color: var(--text-quaternary);
}

.upload-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.delete-icon {
  position: absolute;
  top: var(--spacing-xs);
  right: var(--spacing-xs);
  width: 32rpx;
  height: 32rpx;
  background-color: rgba(0, 0, 0, 0.5);
  border-radius: var(--radius-full);
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--text-inverse);
}