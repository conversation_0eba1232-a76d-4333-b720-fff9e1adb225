// pages/calendar/calendar.js
Page({

  /**
   * 页面的初始数据
   */
  data: {
    selectedDate: '',
    availableDates: ['2025-01-10', '2025-01-12', '2025-01-16', '2025-01-29']
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function (options) {
    if (options.selectedDate) {
      this.setData({
        selectedDate: options.selectedDate
      });
    }
  },

  /**
   * 日期选择事件
   */
  onDateSelect(e) {
    const selectedDate = e.detail.date;
    this.setData({
      selectedDate: selectedDate
    });
    
    // 返回上一页并传递选中的日期
    const pages = getCurrentPages();
    const prevPage = pages[pages.length - 2];
    if (prevPage) {
      prevPage.setData({
        selectedDate: selectedDate,
        selectedQuickDate: '' // 清除快捷选择
      });
    }
    
    wx.navigateBack();
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady: function () {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow: function () {

  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide: function () {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload: function () {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh: function () {

  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom: function () {

  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage: function () {

  }
})
