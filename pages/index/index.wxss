/* pages/index/index.wxss */
page {
  background-color: #f5f5f5;
  height: 100vh;
}

.container {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background: linear-gradient(135deg, #4A90E2 0%, #357ABD 100%);
}

/* 主要内容区域 */
.main-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 30px 20px 20px 20px;
  background-color: #f5f5f5;
  border-radius: 15px 15px 0 0;
  margin-top: 15px;
}

/* Logo区域 */
.logo-section {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 50px;
}

.logo-circle {
  width: 100px;
  height: 100px;
  border-radius: 50%;
  background: linear-gradient(135deg, #4A90E2 0%, #357ABD 100%);
  display: flex;
  justify-content: center;
  align-items: center;
  margin-bottom: 18px;
  box-shadow: 0 6px 20px rgba(74, 144, 226, 0.25);
}

.center-title {
  font-size: 20px;
  font-weight: 600;
  color: #333;
  text-align: center;
}

/* 功能卡片区域 */
.function-cards {
  width: 100%;
  display: flex;
  flex-direction: column;
  gap: 16px;
  margin-bottom: 50px;
  padding: 0 5px;
}

.function-card {
  width: 100%;
  height: 100px;
  border-radius: 12px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  box-shadow: 0 3px 12px rgba(0, 0, 0, 0.08);
  transition: transform 0.2s ease-in-out;
}

.function-card:active {
  transform: translateY(1px);
}

.booking-card {
  background: linear-gradient(135deg, #4A90E2 0%, #357ABD 100%);
}

.suggestion-card {
  background: linear-gradient(135deg, #4A90E2 0%, #357ABD 100%);
}

.card-icon {
  margin-bottom: 8px;
}

.card-title {
  font-size: 16px;
  font-weight: 600;
  color: #fff;
}

/* 联系信息区域 */
.contact-section {
  text-align: center;
  margin-top: auto;
  padding-bottom: 20px;
}

.contact-line {
  display: block;
  font-size: 12px;
  color: #999;
  line-height: 1.8;
}

