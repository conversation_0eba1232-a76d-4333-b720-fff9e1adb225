/* pages/index/index.wxss */
page {
  background-color: #f7f8fa;
}

.container {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  padding-bottom: 50px; /* Space for tabbar */
}

.custom-navbar {
  background-color: #fff;
  border-bottom: 1px solid #eee;
}

.navbar-right-icons {
  display: flex;
  align-items: center;
}

.navbar-right-icons van-icon {
  margin-left: 15px;
}

.content-area {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 20px;
  padding-top: calc(20px + var(--status-bar-height) + 44px); /* Adjust for custom nav bar */
}

.logo-section {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 40px;
}

.logo-circle {
  width: 100px;
  height: 100px;
  border-radius: 50%;
  background-color: #007AFF;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-bottom: 15px;
  box-shadow: 0 4px 15px rgba(0, 122, 255, 0.2);
}

.car-icon {
  width: 60px;
  height: 60px;
}

.center-name {
  font-size: 24px;
  font-weight: bold;
  color: #333;
}

.action-cards {
  width: 100%;
  display: flex;
  flex-direction: column;
  gap: 20px;
  margin-bottom: 40px;
}

.card {
  background-color: #fff;
  border-radius: 12px;
  padding: 30px 20px;
  display: flex;
  flex-direction: column;
  align-items: center;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.08);
  transition: transform 0.2s ease-in-out;
}

.card:active {
  transform: translateY(2px);
}

.card-icon-wrapper {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  background-color: #e6f7ff; /* Light blue background for icons */
  display: flex;
  justify-content: center;
  align-items: center;
  margin-bottom: 15px;
}

.apply-card .card-icon-wrapper {
  background-color: #e6f7ff;
}

.suggestion-card .card-icon-wrapper {
  background-color: #fff0f6; /* Light pink background for suggestion icon */
}

.card-text {
  font-size: 18px;
  font-weight: 600;
  color: #333;
}

.contact-info {
  text-align: center;
  color: #888;
  font-size: 14px;
  line-height: 1.6;
  margin-top: auto; /* Pushes to the bottom */
}

.contact-text {
  display: block;
}