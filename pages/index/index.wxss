/* pages/index/index.wxss */
page {
  background-color: #f5f5f5;
  height: 100vh;
}

.container {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background-color: #f5f5f5;
}

/* 顶部区域 */
.top-section {
  background: linear-gradient(135deg, #4A90E2 0%, #357ABD 100%);
  padding-top: calc(env(safe-area-inset-top) + 10px);
  padding-bottom: 40px;
  position: relative;
}

/* 自定义导航栏 */
.custom-navbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 20px;
  height: 44px;
  margin-bottom: 20px;
}

.navbar-title {
  font-size: 18px;
  font-weight: 600;
  color: #fff;
}

.navbar-buttons {
  display: flex;
  gap: 12px;
}

.navbar-button {
  width: 36px;
  height: 36px;
  background-color: rgba(255, 255, 255, 0.2);
  border-radius: 18px;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 主要内容区域 */
.main-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 30px 20px 20px 20px;
  background-color: #f5f5f5;
}

/* Logo区域 */
.logo-section {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 30px;
  padding: 0 20px;
}

.logo-circle {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  background-color: rgba(255, 255, 255, 0.2);
  display: flex;
  justify-content: center;
  align-items: center;
  margin-bottom: 15px;
}

.center-title {
  font-size: 18px;
  font-weight: 600;
  color: #fff;
  text-align: center;
}

/* 功能卡片区域 */
.function-cards {
  width: 100%;
  display: flex;
  flex-direction: column;
  gap: 20px;
  margin-bottom: 60px;
  padding: 0 5px;
}

.function-card {
  width: 100%;
  height: 120px;
  border-radius: 16px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
  transition: transform 0.2s ease-in-out;
  position: relative;
  overflow: hidden;
}

.function-card:active {
  transform: translateY(2px);
}

.booking-card {
  background: linear-gradient(135deg, #4A90E2 0%, #357ABD 100%);
}

.suggestion-card {
  background: linear-gradient(135deg, #4A90E2 0%, #357ABD 100%);
}

.card-icon-wrapper {
  margin-bottom: 12px;
}

.card-icon-circle {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  background-color: #fff;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.card-title {
  font-size: 18px;
  font-weight: 600;
  color: #fff;
}

/* 联系信息区域 */
.contact-section {
  text-align: center;
  margin-top: auto;
  padding: 30px 0;
}

.contact-line {
  display: block;
  font-size: 13px;
  color: #999;
  line-height: 1.6;
}

