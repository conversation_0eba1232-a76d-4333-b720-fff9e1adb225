// pages/index/index.js

Page({

  /**
   * 页面的初始数据
   */
  data: {
    active: 0, // For tabbar
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function (options) {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow: function () {

  },

  /**
   * "模拟预约"按钮点击事件：跳转到预约页面
   */
  goApply() {
    wx.navigateTo({
      url: '/pages/apply/apply'
    })
  },

  /**
   * "投诉建议"按钮点击事件：跳转到投诉建议页面
   */
  goSuggestion() {
    wx.navigateTo({
      url: '/pages/suggestion/suggestion'
    })
  },

  /**
   * Tabbar 切换事件
   */
  onChange(event) {
    this.setData({ active: event.detail });
    if (event.detail === 0) {
      wx.switchTab({
        url: '/pages/index/index'
      });
    } else if (event.detail === 1) {
      wx.switchTab({
        url: '/pages/my/index'
      });
    }
  }
})
