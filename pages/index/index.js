// pages/index/index.js

Page({

  /**
   * 页面的初始数据
   */
  data: {

  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function () {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow: function () {

  },

  /**
   * "模拟预约"按钮点击事件：跳转到预约页面
   */
  goBooking() {
    wx.navigateTo({
      url: '/pages/booking/booking'
    })
  },

  /**
   * "投诉建议"按钮点击事件：跳转到投诉建议页面
   */
  goSuggestion() {
    wx.navigateTo({
      url: '/pages/suggestion/suggestion'
    })
  },

  /**
   * 跳转到"我的"页面
   */
  goToMy() {
    wx.switchTab({
      url: '/pages/my/index'
    });
  }
})
