<!-- pages/invoice/index.wxml -->
<view class="container">
  <view class="header">
    <text class="title">开票信息</text>
  </view>
  
  <view class="content">
    <view class="form-section">
      <view class="form-item">
        <text class="label">发票类型</text>
        <picker bindchange="onInvoiceTypeChange" value="{{selectedInvoiceType}}" range="{{invoiceTypes}}">
          <view class="picker">
            {{invoiceTypes[selectedInvoiceType]}}
            <image class="arrow" src="/images/icon/next.svg"></image>
          </view>
        </picker>
      </view>
      
      <view class="form-item">
        <text class="label">发票抬头</text>
        <input class="input" placeholder="请输入发票抬头" value="{{invoiceInfo.title}}" data-field="title" bindinput="onInputChange" />
      </view>
      
      <!-- 企业发票需要填写税号 -->
      <view class="form-item" wx:if="{{selectedInvoiceType == 1}}">
        <text class="label">税号</text>
        <input class="input" placeholder="请输入企业税号" value="{{invoiceInfo.taxNumber}}" data-field="taxNumber" bindinput="onInputChange" />
      </view>
      
      <view class="form-item">
        <text class="label">发票内容</text>
        <picker bindchange="onInvoiceContentChange" value="{{selectedInvoiceContent}}" range="{{invoiceContents}}">
          <view class="picker">
            {{invoiceContents[selectedInvoiceContent]}}
            <image class="arrow" src="/images/icon/next.svg"></image>
          </view>
        </picker>
      </view>
      
      <!-- 企业发票的详细信息 -->
      <view wx:if="{{selectedInvoiceType == 1}}">
        <view class="form-item">
          <text class="label">地址</text>
          <input class="input" placeholder="请输入企业地址" value="{{invoiceInfo.address}}" data-field="address" bindinput="onInputChange" />
        </view>
        
        <view class="form-item">
          <text class="label">电话</text>
          <input class="input" placeholder="请输入企业电话" value="{{invoiceInfo.phone}}" data-field="phone" bindinput="onInputChange" />
        </view>
        
        <view class="form-item">
          <text class="label">开户行</text>
          <input class="input" placeholder="请输入开户行" value="{{invoiceInfo.bank}}" data-field="bank" bindinput="onInputChange" />
        </view>
        
        <view class="form-item">
          <text class="label">银行账户</text>
          <input class="input" placeholder="请输入银行账户" value="{{invoiceInfo.bankAccount}}" data-field="bankAccount" bindinput="onInputChange" />
        </view>
      </view>
    </view>
    
    <view class="button-container">
      <button class="submit-btn" bindtap="onSubmit">提交开票申请</button>
    </view>
  </view>
</view>