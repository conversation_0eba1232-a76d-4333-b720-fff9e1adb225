/* pages/invoice/index.wxss */
.container {
  min-height: 100vh;
  background-color: var(--bg-primary);
  padding-bottom: calc(env(safe-area-inset-bottom) + var(--spacing-xl));
}

.header {
  background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
  padding: var(--spacing-xl) var(--spacing-lg);
  text-align: center;
  position: relative;
}

.header::after {
  content: '';
  position: absolute;
  bottom: -20rpx;
  left: 0;
  width: 100%;
  height: 40rpx;
  background: linear-gradient(to top, var(--bg-primary), transparent);
}

.title {
  color: var(--text-inverse);
  font-size: var(--font-size-xl);
  font-weight: 600;
  position: relative;
  z-index: 1;
}

.content {
  padding: var(--spacing-lg);
  margin-top: var(--spacing-md);
}

.form-section {
  background-color: var(--bg-card);
  border-radius: var(--radius-lg);
  padding: 0 var(--spacing-lg);
  margin-bottom: var(--spacing-lg);
  box-shadow: var(--shadow-sm);
}

.section-title {
  font-size: var(--font-size-md);
  font-weight: 500;
  color: var(--text-secondary);
  padding: var(--spacing-md) 0;
  border-bottom: 2rpx solid var(--border-color);
  margin-bottom: var(--spacing-sm);
}

.form-item {
  display: flex;
  align-items: center;
  padding: var(--spacing-lg) 0;
  border-bottom: 2rpx solid var(--border-color);
  transition: background-color var(--transition-fast);
}

.form-item:last-child {
  border-bottom: none;
}

.form-item:active {
  background-color: var(--bg-tertiary);
  margin: 0 calc(-1 * var(--spacing-lg));
  padding: var(--spacing-lg) var(--spacing-lg);
}

.label {
  width: 180rpx;
  font-size: var(--font-size-md);
  color: var(--text-primary);
  font-weight: 500;
}

.input {
  flex: 1;
  font-size: var(--font-size-md);
  padding: var(--spacing-sm) 0;
  color: var(--text-secondary);
  border: none;
  background: transparent;
}

.input:focus {
  color: var(--text-primary);
}

.picker {
  flex: 1;
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: var(--font-size-md);
  color: var(--text-secondary);
}

.picker-text {
  flex: 1;
}

.picker-placeholder {
  color: var(--text-quaternary);
}

.arrow {
  width: 32rpx;
  height: 32rpx;
  color: var(--text-quaternary);
  margin-left: var(--spacing-sm);
}

.button-container {
  padding: var(--spacing-lg) 0;
  margin-top: var(--spacing-lg);
}

.submit-btn {
  background-color: var(--primary-color);
  color: var(--text-inverse);
  font-size: var(--font-size-md);
  font-weight: 500;
  border-radius: var(--radius-lg);
  height: 88rpx;
  line-height: 88rpx;
  box-shadow: var(--shadow-md);
  transition: all var(--transition-fast);
  display: flex;
  align-items: center;
  justify-content: center;
}

.submit-btn:active {
  background-color: var(--primary-dark);
  transform: translateY(2rpx);
  box-shadow: var(--shadow-sm);
}

.submit-btn::after {
  border: none;
}

.tips {
  background-color: rgba(0, 102, 204, 0.1);
  border-radius: var(--radius-md);
  padding: var(--spacing-md);
  margin-bottom: var(--spacing-lg);
}

.tips-title {
  font-size: var(--font-size-md);
  font-weight: 500;
  color: var(--primary-color);
  margin-bottom: var(--spacing-sm);
  display: flex;
  align-items: center;
}

.tips-icon {
  width: 32rpx;
  height: 32rpx;
  margin-right: var(--spacing-sm);
}

.tips-content {
  font-size: var(--font-size-sm);
  color: var(--text-secondary);
  line-height: var(--line-height-md);
}

.required {
  color: var(--error-color);
  margin-right: var(--spacing-xs);
}