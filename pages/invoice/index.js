// pages/invoice/index.js
Page({

    /**
     * 页面的初始数据
     */
    data: {
        // 发票信息
        invoiceInfo: {
            title: '',
            taxNumber: '',
            address: '',
            phone: '',
            bank: '',
            bankAccount: ''
        },
        // 发票类型选项
        invoiceTypes: ['个人', '企业'],
        selectedInvoiceType: 0,
        // 发票内容选项
        invoiceContents: ['培训费', '服务费', '其他'],
        selectedInvoiceContent: 0
    },

    /**
     * 生命周期函数--监听页面加载
     */
    onLoad: function (options) {

    },

    /**
     * 生命周期函数--监听页面显示
     */
    onShow: function () {

    },

    /**
     * 生命周期函数--监听页面隐藏
     */
    onHide: function () {

    },

    /**
     * 生命周期函数--监听页面卸载
     */
    onUnload: function () {

    },

    /**
     * 页面相关事件处理函数--监听用户下拉动作
     */
    onPullDownRefresh: function () {

    },

    /**
     * 页面上拉触底事件的处理函数
     */
    onReachBottom: function () {

    },

    /**
     * 用户点击右上角分享
     */
    onShareAppMessage: function () {

    },

    /**
     * 发票类型选择
     */
    onInvoiceTypeChange: function (e) {
        this.setData({
            selectedInvoiceType: e.detail.value
        });
    },

    /**
     * 发票内容选择
     */
    onInvoiceContentChange: function (e) {
        this.setData({
            selectedInvoiceContent: e.detail.value
        });
    },

    /**
     * 输入框变化处理
     */
    onInputChange: function (e) {
        const field = e.currentTarget.dataset.field;
        const value = e.detail.value;
        const invoiceInfo = this.data.invoiceInfo;
        invoiceInfo[field] = value;
        this.setData({
            invoiceInfo: invoiceInfo
        });
    },

    /**
     * 提交开票申请
     */
    onSubmit: function () {
        const invoiceInfo = this.data.invoiceInfo;
        // 简单验证
        if (!invoiceInfo.title) {
            wx.showToast({
                title: '请填写发票抬头',
                icon: 'none'
            });
            return;
        }

        // 如果是企业发票，需要填写税号
        if (this.data.selectedInvoiceType == 1 && !invoiceInfo.taxNumber) {
            wx.showToast({
                title: '企业发票需填写税号',
                icon: 'none'
            });
            return;
        }

        // 这里可以添加提交逻辑
        wx.showToast({
            title: '开票申请已提交',
            icon: 'success'
        });

        // 模拟提交后返回上一页
        setTimeout(() => {
            wx.navigateBack();
        }, 1500);
    }
})