// pages/booking-confirm/booking-confirm.js
Page({

  /**
   * 页面的初始数据
   */
  data: {
    bookingInfo: {},
    countdownText: '14:59',
    countdownTimer: null,
    remainingSeconds: 15 * 60 // 15分钟倒计时
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function (options) {
    // 获取传递的预约数据
    if (options.data) {
      try {
        const bookingData = JSON.parse(decodeURIComponent(options.data));
        this.processBookingData(bookingData);
      } catch (error) {
        console.error('解析预约数据失败:', error);
        wx.showToast({
          title: '数据错误',
          icon: 'none'
        });
      }
    }
    
    // 启动倒计时
    this.startCountdown();
  },

  /**
   * 处理预约数据
   */
  processBookingData(data) {
    const now = new Date();
    const createTime = `${now.getFullYear()}-${String(now.getMonth() + 1).padStart(2, '0')}-${String(now.getDate()).padStart(2, '0')} ${String(now.getHours()).padStart(2, '0')}:${String(now.getMinutes()).padStart(2, '0')}:${String(now.getSeconds()).padStart(2, '0')}`;
    
    // 转换科目文本
    const subjectText = data.subject === 'subject2' ? '科目二' : '科目三';
    
    // 转换车型文本
    const carTypeText = data.carType === 'C1' ? '小型汽车(C1)' : '小型手动挡汽车(C2)';
    
    // 转换时间段文本
    const timeSlotMap = {
      'morning1': '08:00-10:00',
      'morning2': '10:00-12:00',
      'afternoon1': '14:00-16:00',
      'afternoon2': '16:00-18:00'
    };
    const timeSlotText = timeSlotMap[data.timeSlot] || data.timeSlot;
    
    // 格式化考试日期
    const examDate = this.formatExamDate(data.date);
    
    const bookingInfo = {
      createTime: createTime,
      subjectText: subjectText,
      carTypeText: carTypeText,
      examDate: examDate,
      timeSlotText: timeSlotText,
      examCount: data.examCount || 1,
      price: data.price || 150
    };
    
    this.setData({
      bookingInfo: bookingInfo
    });
  },

  /**
   * 格式化考试日期
   */
  formatExamDate(dateStr) {
    const date = new Date(dateStr);
    const weekDays = ['周日', '周一', '周二', '周三', '周四', '周五', '周六'];
    const weekDay = weekDays[date.getDay()];
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    return `${date.getFullYear()}-${month}-${day} ${weekDay}`;
  },

  /**
   * 启动倒计时
   */
  startCountdown() {
    this.data.countdownTimer = setInterval(() => {
      if (this.data.remainingSeconds <= 0) {
        this.stopCountdown();
        this.handleTimeout();
        return;
      }
      
      this.data.remainingSeconds--;
      const minutes = Math.floor(this.data.remainingSeconds / 60);
      const seconds = this.data.remainingSeconds % 60;
      const countdownText = `${String(minutes).padStart(2, '0')}:${String(seconds).padStart(2, '0')}`;
      
      this.setData({
        countdownText: countdownText
      });
    }, 1000);
  },

  /**
   * 停止倒计时
   */
  stopCountdown() {
    if (this.data.countdownTimer) {
      clearInterval(this.data.countdownTimer);
      this.data.countdownTimer = null;
    }
  },

  /**
   * 处理超时
   */
  handleTimeout() {
    wx.showModal({
      title: '预约超时',
      content: '支付时间已超时，预约已自动取消',
      showCancel: false,
      success: () => {
        wx.navigateBack();
      }
    });
  },

  /**
   * 立即支付
   */
  makePayment() {
    // 停止倒计时
    this.stopCountdown();
    
    // 模拟支付流程
    wx.showLoading({
      title: '正在支付...'
    });
    
    setTimeout(() => {
      wx.hideLoading();
      wx.showModal({
        title: '支付成功',
        content: '预约已确认，请按时参加考试',
        showCancel: false,
        success: () => {
          // 跳转到订单页面或首页
          wx.switchTab({
            url: '/pages/index/index'
          });
        }
      });
    }, 2000);
  },

  /**
   * 返回上一页
   */
  goBack() {
    wx.showModal({
      title: '确认返回',
      content: '返回将取消当前预约，确定要返回吗？',
      success: (res) => {
        if (res.confirm) {
          this.stopCountdown();
          wx.navigateBack();
        }
      }
    });
  },

  /**
   * 回到首页
   */
  goHome() {
    wx.showModal({
      title: '确认离开',
      content: '离开将取消当前预约，确定要离开吗？',
      success: (res) => {
        if (res.confirm) {
          this.stopCountdown();
          wx.switchTab({
            url: '/pages/index/index'
          });
        }
      }
    });
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady: function () {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow: function () {

  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide: function () {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload: function () {
    // 页面卸载时清除倒计时
    this.stopCountdown();
  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh: function () {

  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom: function () {

  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage: function () {

  }
})
