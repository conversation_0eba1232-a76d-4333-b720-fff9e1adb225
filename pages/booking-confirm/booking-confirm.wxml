<!--pages/booking-confirm/booking-confirm.wxml-->
<view class="container">
  <!-- 顶部导航栏 -->
  <view class="top-navbar">
    <van-icon name="arrow-left" size="20px" color="#fff" bindtap="goBack" />
    <text class="navbar-title">模拟考试预约</text>
    <view class="navbar-right">
      <van-icon name="ellipsis" size="20px" color="#fff" />
      <van-icon name="eye-o" size="20px" color="#fff" />
    </view>
  </view>

  <!-- 主要内容区域 -->
  <view class="main-content">
    <!-- 成功状态 -->
    <view class="success-section">
      <view class="success-icon">
        <van-icon name="success" size="60px" color="#4A90E2" />
      </view>
      <text class="success-title">预约已提交</text>
      <text class="success-subtitle">请在15分钟内完成付款，超时将自动取消</text>
      <text class="countdown-text">支付剩余时间：{{countdownText}}</text>
    </view>

    <!-- 预约信息 -->
    <view class="booking-info-section">
      <text class="section-title">预约信息</text>
      <view class="info-list">
        <view class="info-item">
          <text class="info-label">创建时间</text>
          <text class="info-value">{{bookingInfo.createTime}}</text>
        </view>
        <view class="info-item">
          <text class="info-label">考试科目</text>
          <text class="info-value">{{bookingInfo.subjectText}}</text>
        </view>
        <view class="info-item">
          <text class="info-label">车型</text>
          <text class="info-value">{{bookingInfo.carTypeText}}</text>
        </view>
        <view class="info-item">
          <text class="info-label">考试日期</text>
          <text class="info-value">{{bookingInfo.examDate}}</text>
        </view>
        <view class="info-item">
          <text class="info-label">时间段</text>
          <text class="info-value">{{bookingInfo.timeSlotText}}</text>
        </view>
        <view class="info-item">
          <text class="info-label">考试条数</text>
          <text class="info-value">{{bookingInfo.examCount}}条</text>
        </view>
        <view class="info-item total">
          <text class="info-label">应付金额</text>
          <text class="info-value price">{{bookingInfo.price}}元</text>
        </view>
      </view>
    </view>

    <!-- 支付按钮 -->
    <view class="payment-section">
      <view class="payment-button" bindtap="makePayment">
        <text class="button-text">立即支付</text>
      </view>
    </view>

    <!-- 注意事项 -->
    <view class="notice-section">
      <text class="notice-title">注意事项</text>
      <view class="notice-list">
        <view class="notice-item">
          <text class="notice-dot">•</text>
          <text class="notice-text">请在15分钟内完成支付，超时预约将自动取消</text>
        </view>
        <view class="notice-item">
          <text class="notice-dot">•</text>
          <text class="notice-text">考试请携带身份证等有效证件</text>
        </view>
        <view class="notice-item">
          <text class="notice-dot">•</text>
          <text class="notice-text">如需取消预约，请提前24小时操作，否则将扣除考试费用</text>
        </view>
        <view class="notice-item">
          <text class="notice-dot">•</text>
          <text class="notice-text">请提前15分钟到达考场，迟到可能无法参加考试</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 底部导航栏 -->
  <view class="bottom-tabbar">
    <view class="tab-item" bindtap="goHome">
      <van-icon name="home-o" size="24px" color="#999" />
      <text class="tab-text">首页</text>
    </view>
    <view class="tab-item">
      <van-icon name="user-o" size="24px" color="#999" />
      <text class="tab-text">我的</text>
    </view>
  </view>
</view>
