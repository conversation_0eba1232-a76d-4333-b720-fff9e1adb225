/* pages/booking-confirm/booking-confirm.wxss */
page {
  background-color: #f5f5f5;
  height: 100vh;
}

.container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background: linear-gradient(135deg, #4A90E2 0%, #357ABD 100%);
}

/* 顶部导航栏 */
.top-navbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: calc(env(safe-area-inset-top) + 10px) 20px 20px 20px;
  background: linear-gradient(135deg, #4A90E2 0%, #357ABD 100%);
}

.navbar-title {
  font-size: 18px;
  font-weight: 600;
  color: #fff;
}

.navbar-right {
  display: flex;
  align-items: center;
  gap: 15px;
}

/* 主要内容区域 */
.main-content {
  flex: 1;
  background-color: #f5f5f5;
  border-radius: 20px 20px 0 0;
  padding: 30px 20px 20px 20px;
  overflow-y: auto;
}

/* 成功状态区域 */
.success-section {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 40px;
}

.success-icon {
  width: 100px;
  height: 100px;
  background-color: #f0f8ff;
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-bottom: 20px;
}

.success-title {
  font-size: 20px;
  font-weight: 600;
  color: #333;
  margin-bottom: 10px;
}

.success-subtitle {
  font-size: 14px;
  color: #666;
  text-align: center;
  margin-bottom: 10px;
}

.countdown-text {
  font-size: 14px;
  color: #4A90E2;
  font-weight: 600;
}

/* 预约信息区域 */
.booking-info-section {
  background-color: #fff;
  border-radius: 15px;
  padding: 20px;
  margin-bottom: 30px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
}

.section-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  margin-bottom: 20px;
  display: block;
}

.info-list {
  display: flex;
  flex-direction: column;
}

.info-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 0;
  border-bottom: 1px solid #f5f5f5;
}

.info-item:last-child {
  border-bottom: none;
}

.info-item.total {
  padding-top: 20px;
  border-top: 2px solid #f0f0f0;
  margin-top: 10px;
}

.info-label {
  font-size: 14px;
  color: #666;
}

.info-value {
  font-size: 14px;
  color: #333;
  font-weight: 500;
}

.info-value.price {
  font-size: 16px;
  color: #4A90E2;
  font-weight: 600;
}

/* 支付按钮 */
.payment-section {
  margin-bottom: 30px;
}

.payment-button {
  width: 100%;
  height: 50px;
  background: linear-gradient(135deg, #4A90E2 0%, #357ABD 100%);
  border-radius: 25px;
  display: flex;
  justify-content: center;
  align-items: center;
  box-shadow: 0 4px 15px rgba(74, 144, 226, 0.3);
}

.button-text {
  font-size: 16px;
  font-weight: 600;
  color: #fff;
}

/* 注意事项 */
.notice-section {
  background-color: #fff;
  border-radius: 15px;
  padding: 20px;
  margin-bottom: 80px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
}

.notice-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  margin-bottom: 15px;
  display: block;
}

.notice-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.notice-item {
  display: flex;
  align-items: flex-start;
  gap: 8px;
}

.notice-dot {
  font-size: 14px;
  color: #4A90E2;
  font-weight: 600;
  line-height: 1.5;
  margin-top: 1px;
}

.notice-text {
  font-size: 13px;
  color: #666;
  line-height: 1.5;
  flex: 1;
}

/* 底部导航栏 */
.bottom-tabbar {
  display: flex;
  background-color: #fff;
  border-top: 1px solid #eee;
  padding-bottom: env(safe-area-inset-bottom);
}

.tab-item {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 8px 0;
  transition: all 0.2s ease;
}

.tab-text {
  font-size: 12px;
  color: #999;
  margin-top: 4px;
}
