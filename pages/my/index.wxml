<view class="top-bg"></view>
<view class="box">
  <!-- 头像 -->
  <view class="head-box">
    <button open-type="chooseAvatar" class="avatar" bind:chooseavatar="chooseAvatar">
      <image class="head-img" src="{{userInfo.avatarUrl ? avatarUrl : '/images/default.png'}}" mode="widthFix"></image>
    </button>
    <view class="tip">{{userInfo.openId?'欢迎使用,' + (userInfo.nickName ? userInfo.nickName : '微信用户') : '当前未登录，请登录！'}}</view>
    <view class="user-status" wx:if="{{userInfo.openId}}">已登录</view>
  </view>
  
  <!-- 第一部分列表 -->
  <view class="section">
    <view class="section-title">账户信息</view>
    <button class="row" bindtap="nickNameClick">
      <view class="left">
        <icon class="row-icon" type="success" size="20"></icon>
        <text class="text">修改昵称</text>
      </view>
      <view class="right">
        <text>{{ userInfo.nickName ? userInfo.nickName : '微信用户' }}</text>
        <icon class="right-icon" type="success" size="16"></icon>
      </view>
    </button>
    <button class="row" bindtap="getPhoneClick">
      <view class="left">
        <icon class="row-icon" type="success" size="20"></icon>
        <text class="text">授权手机号</text>
      </view>
      <view class="right">
        <text>{{ userInfo.phone ? userInfo.phone : '未授权' }}</text>
        <icon class="right-icon" type="success" size="16"></icon>
      </view>
    </button>
  </view>
  
  <!-- 新增功能按钮 -->
  <view class="section">
    <view class="section-title">我的服务</view>
    <button class="row" bindtap="myOrdersClick">
      <view class="left">
        <icon class="row-icon" type="success" size="20"></icon>
        <text class="text">我的订单</text>
      </view>
      <view class="right">
        <icon class="right-icon" type="success" size="16"></icon>
      </view>
    </button>
    <button class="row" bindtap="invoiceClick">
      <view class="left">
        <icon class="row-icon" type="success" size="20"></icon>
        <text class="text">开票</text>
      </view>
      <view class="right">
        <icon class="right-icon" type="success" size="16"></icon>
      </view>
    </button>
    <button class="row" bindtap="refundClick">
      <view class="left">
        <icon class="row-icon" type="success" size="20"></icon>
        <text class="text">退款</text>
      </view>
      <view class="right">
        <icon class="right-icon" type="success" size="16"></icon>
      </view>
    </button>
  </view>
  
  <!-- 退出登录按钮 -->
  <button class="btn-logout" wx:if="{{userInfo.openId}}" bindtap="logout">退出登录</button>
</view>

<get-phone show="{{ getPhoneShow }}" bind:getPhone="handleSetWxUser"/>
<nick-name show="{{ nickNameShow }}" bind:getNickName="handleSetWxUser"/>