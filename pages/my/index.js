// pages/my/index.js
const AUTH = require('../../utils/auth')
import {
  request
} from '../../config/request'
var api = require('../../config/api.js')
var config = require('../../config/config.js').Config

Page({

  /**
   * 页面的初始数据
   */
  data: {
    getPhoneShow: false,
    nickNameShow: false,
    userInfo: {},
    avatarUrl: '',
    isNormalUser: true
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function (options) {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow: function () {
    this.init()
  },

  async init() {
    await AUTH.loginPromise()
    this.getUserApiInfo()
  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage: function () {

  },

  isNormalUser() {
    this.setData({
      isNormalUser: this.data.userInfo.roles.length === 1 && this.data.userInfo.roles[0].id === '1858750179457888256'
    })
  },

  async chooseAvatar(e) {
    console.log(e);
    const avatarUrl = e.detail.avatarUrl
    let res = await request(api.UploadFile, '', avatarUrl, 'upload')
    await request(api.WxUserAvatarUpdate, {}, {
      fileId: res.data.data.fileId
    })
    wx.showToast({
      title: '头像设置成功',
    })
    this.getUserApiInfo()
  },

  handleSetWxUser(e) {
    this.setData({
      userInfo: e.detail
    })
  },

  nickNameClick() {
    this.setData({
      nickNameShow: true
    })
  },

  getPhoneClick() {
    this.setData({
      getPhoneShow: true
    })
  },

  getUserApiInfo() {
    request(api.WxUserInfo, '', '').then(res => {
      this.setData({
        userInfo: res.data
      })
      this.isNormalUser()
      if (this.data.userInfo && this.data.userInfo.avatarUrl) {
        request(api.DownloadFile, {}, {
          fileId: this.data.userInfo.avatarUrl
        }, 'download').then(res => {
          this.setData({
            avatarUrl: res.tempFilePath
          })
        })
      }
    })
  },

  myOrdersClick() {
    wx.navigateTo({
      url: '/pages/orders/index'
    })
  },

  invoiceClick() {
    wx.navigateTo({
      url: '/pages/invoice/index'
    })
  },

  refundClick() {
    wx.navigateTo({
      url: '/pages/refund/index'
    })
  }
})