
/* 设置背景颜色 */
.top-bg {
  height: 300rpx;
  background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
  position: relative;
}

.top-bg::after {
  content: '';
  position: absolute;
  bottom: -50rpx;
  left: 0;
  width: 100%;
  height: 100rpx;
  background: linear-gradient(to top, var(--bg-primary), transparent);
  z-index: 1;
}
 
/* 圆角盒子 */
.box {
  background-color: var(--bg-card);
  margin: -150rpx var(--spacing-lg) var(--spacing-lg) var(--spacing-lg);
  border-radius: var(--radius-xl);
  box-shadow: var(--shadow-lg);
  position: relative;
  z-index: 2;
  overflow: hidden;
}
 
/* 头像部分 */
.head-box {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  padding: var(--spacing-xl) var(--spacing-lg);
  background: linear-gradient(to bottom, rgba(0, 102, 204, 0.05), transparent);
  position: relative;
}

.head-box::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 4rpx;
  background: linear-gradient(90deg, var(--primary-color), var(--primary-light), var(--primary-color));
  animation: shimmer 2s infinite;
}

@keyframes shimmer {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}
 
.avatar {
  margin-top: -80rpx;
  font-weight: inherit;
  display: flex;
  justify-content: center;
  background-color: rgba(0, 0, 0, 0);
  position: relative;
  z-index: 3;
}
 
.avatar::after {
  border: none;
}
 
.head-img {
  width: 160rpx;
  height: 160rpx;
  overflow: hidden;
  border-radius: 50%;
  background-color: var(--bg-tertiary);
  border: 4rpx solid var(--bg-card);
  box-shadow: var(--shadow-md);
  transition: transform var(--transition-normal);
}

.head-img:active {
  transform: scale(0.98);
}
 
.tip {
  font-size: var(--font-size-md);
  color: var(--text-secondary);
  margin: var(--spacing-md) 0;
  font-weight: 500;
  text-align: center;
}

.user-status {
  display: inline-block;
  padding: var(--spacing-xs) var(--spacing-sm);
  background-color: var(--primary-color);
  color: var(--text-inverse);
  border-radius: var(--radius-full);
  font-size: var(--font-size-xs);
  margin-top: var(--spacing-xs);
}
 
/* 列表部分 */
.section {
  margin-bottom: var(--spacing-md);
}

.section-title {
  font-size: var(--font-size-md);
  color: var(--text-tertiary);
  padding: var(--spacing-md) var(--spacing-lg);
  font-weight: 500;
}

.row {
  display: flex;
  align-items: center;
  padding: var(--spacing-lg) var(--spacing-lg);
  font-size: var(--font-size-md);
  font-weight: inherit;
  background-color: rgba(0, 0, 0, 0);
  border-bottom: 2rpx solid var(--border-color);
  transition: background-color var(--transition-fast);
  position: relative;
}

.row:active {
  background-color: var(--bg-tertiary);
}

.row:last-child {
  border-bottom: none;
}

.row::after {
  border: none;
}

.row-icon {
  width: 40rpx;
  height: 40rpx;
  margin-right: var(--spacing-md);
  color: var(--primary-color);
}
 
.text {
  margin-left: var(--spacing-sm);
  color: var(--text-primary);
  font-weight: 500;
}
 
.left {
  width: 50%;
  text-align: left;
  display: flex;
  align-items: center;
}
 
.right {
  width: 50%;
  text-align: right;
  color: var(--text-tertiary);
  display: flex;
  align-items: center;
  justify-content: flex-end;
}

.right-icon {
  width: 32rpx;
  height: 32rpx;
  margin-left: var(--spacing-sm);
  color: var(--text-quaternary);
}
 
.end {
  background-color: var(--bg-card);
  margin-top: var(--spacing-md);
  padding: 0 var(--spacing-lg);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-sm);
}
 
.footer {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: var(--spacing-lg) 0;
  font-size: var(--font-size-sm);
  margin: var(--spacing-xl) 0;
  color: var(--text-quaternary);
}

/* 卡片样式 */
.card {
  background-color: var(--bg-card);
  border-radius: var(--radius-lg);
  margin: var(--spacing-md) var(--spacing-lg);
  padding: var(--spacing-lg);
  box-shadow: var(--shadow-sm);
}

.card-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: var(--spacing-md);
  padding-bottom: var(--spacing-md);
  border-bottom: 2rpx solid var(--border-color);
}

.card-title {
  font-size: var(--font-size-lg);
  font-weight: 600;
  color: var(--text-primary);
}

.card-content {
  color: var(--text-secondary);
  font-size: var(--font-size-md);
  line-height: var(--line-height-md);
}

/* 按钮样式 */
.btn-logout {
  width: 80%;
  height: 88rpx;
  background-color: var(--error-color);
  color: var(--text-inverse);
  font-size: var(--font-size-md);
  font-weight: 500;
  border-radius: var(--radius-lg);
  margin: var(--spacing-xl) auto;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: var(--shadow-md);
  transition: all var(--transition-fast);
}

.btn-logout:active {
  background-color: #d70015;
  transform: translateY(2rpx);
  box-shadow: var(--shadow-sm);
}
