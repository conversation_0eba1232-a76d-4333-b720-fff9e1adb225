/* pages/refund/index.wxss */
.container {
  padding: var(--spacing-lg);
  background-color: var(--bg-primary);
  min-height: 100vh;
  padding-bottom: calc(env(safe-area-inset-bottom) + var(--spacing-xl));
}

.header {
  background-color: var(--bg-card);
  padding: var(--spacing-xl);
  text-align: center;
  border-radius: var(--radius-lg);
  margin-bottom: var(--spacing-md);
  box-shadow: var(--shadow-md);
  position: relative;
  overflow: hidden;
}

.header::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 6rpx;
  background: linear-gradient(90deg, var(--warning-color), var(--warning-color), var(--warning-color));
  animation: shimmer 2s infinite;
}

@keyframes shimmer {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

.title {
  font-size: var(--font-size-xl);
  font-weight: 600;
  color: var(--text-primary);
  position: relative;
  z-index: 1;
}

.title-icon {
  width: 48rpx;
  height: 48rpx;
  margin-right: var(--spacing-sm);
  vertical-align: middle;
  color: var(--warning-color);
}

.content {
  background-color: var(--bg-card);
  padding: var(--spacing-xl);
  border-radius: var(--radius-lg);
  margin-bottom: var(--spacing-md);
  box-shadow: var(--shadow-md);
}

.refund-info {
  font-size: var(--font-size-md);
  color: var(--text-secondary);
  text-align: center;
  padding: var(--spacing-xl) 0;
  line-height: var(--line-height-lg);
}

.refund-info-title {
  font-size: var(--font-size-lg);
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: var(--spacing-md);
  display: flex;
  align-items: center;
  justify-content: center;
}

.refund-info-icon {
  width: 40rpx;
  height: 40rpx;
  margin-right: var(--spacing-sm);
  color: var(--warning-color);
}

.refund-steps {
  margin: var(--spacing-lg) 0;
}

.refund-step {
  display: flex;
  margin-bottom: var(--spacing-lg);
  align-items: flex-start;
}

.step-number {
  width: 40rpx;
  height: 40rpx;
  border-radius: var(--radius-full);
  background-color: var(--warning-color);
  color: var(--text-inverse);
  font-size: var(--font-size-sm);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: var(--spacing-md);
  flex-shrink: 0;
}

.step-content {
  flex: 1;
}

.step-title {
  font-size: var(--font-size-md);
  font-weight: 500;
  color: var(--text-primary);
  margin-bottom: var(--spacing-xs);
}

.step-desc {
  font-size: var(--font-size-sm);
  color: var(--text-tertiary);
  line-height: var(--line-height-md);
}

.refund-tips {
  background-color: rgba(255, 149, 0, 0.1);
  border-radius: var(--radius-md);
  padding: var(--spacing-md);
  margin-top: var(--spacing-lg);
}

.tips-title {
  font-size: var(--font-size-md);
  font-weight: 500;
  color: var(--warning-color);
  margin-bottom: var(--spacing-sm);
  display: flex;
  align-items: center;
}

.tips-icon {
  width: 32rpx;
  height: 32rpx;
  margin-right: var(--spacing-sm);
}

.tips-content {
  font-size: var(--font-size-sm);
  color: var(--text-secondary);
  line-height: var(--line-height-md);
}

.footer {
  text-align: center;
  margin-top: var(--spacing-xl);
}

.back-btn {
  width: 80%;
  background-color: var(--primary-color);
  color: var(--text-inverse);
  border-radius: var(--radius-lg);
  font-size: var(--font-size-md);
  font-weight: 500;
  padding: var(--spacing-md) 0;
  box-shadow: var(--shadow-md);
  transition: all var(--transition-fast);
  display: flex;
  align-items: center;
  justify-content: center;
}

.back-btn:active {
  background-color: var(--primary-dark);
  transform: translateY(2rpx);
  box-shadow: var(--shadow-sm);
}

.back-btn-icon {
  width: 32rpx;
  height: 32rpx;
  margin-right: var(--spacing-sm);
}