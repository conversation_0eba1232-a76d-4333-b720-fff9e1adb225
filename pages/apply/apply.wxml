<!--pages/apply/apply.wxml-->
<view class="apply">
  <view class="title">
    访客申请  
  </view>

  <van-cell-group style="width: 100%;" inset>
    <van-cell size="large" wx:if="{{ !isNormalUser }}" title="登记类型" required="true" is-link data-type="front" bindtap="openLogTypePopup" label="{{ formErr.logTypeErrMsg }}" value="{{ form.logTypeLabel }}" label-class="err-label" />
    <van-cell size="large" title="来访日期" required="true" is-link data-type="front" bindtap="openVisitDatePopup" label="{{ formErr.visitDateErrMsg }}" value="{{ form.visitDate }}" label-class="err-label" />
    <view wx:for="{{ visitors }}" wx:key="index">
      <van-cell size="large" title="来访者{{ index + 1 }}">
        <van-icon wx:if="{{ index !== 0 }}" slot="right-icon" name="delete" class="custom-icon" data-idx="{{index}}" bindtap="deleteVisitor"/>
      </van-cell>
      <van-field size="large" value="{{ item.name }}" label="姓名{{ index + 1 }}" placeholder="请输入姓名{{ index + 1 }}" data-modal="name" data-idx="{{ index }}" required="true" bind:change="onVisitorChange" error="{{ item.nameErr }}" error-message="{{ item.nameErrMsg }}" />
      <van-field size="large" value="{{ item.identity }}" label="身份证{{ index + 1 }}" placeholder="请输入身份证{{ index + 1 }}" data-modal="identity" data-idx="{{ index }}" required="{{ form.logType === 'normal' }}" bind:change="onVisitorChange" error="{{ item.identityErr }}" error-message="{{ item.identityErrMsg }}" />
      <van-field size="large" value="{{ item.phone }}" label="联系方式{{ index + 1 }}" placeholder="请输入联系方式{{ index + 1 }}"  data-modal="phone" data-idx="{{ index }}" required="{{ form.logType === 'normal' }}" bind:change="onVisitorChange" error="{{ item.phoneErr }}" error-message="{{ item.phoneErrMsg }}">
        <van-icon wx:if="{{ index === visitors.length - 1 }}" slot="right-icon" name="add" class="custom-icon" bindtap="addVisitor" />
      </van-field>
    </view>
    <van-field size="large" value="{{ form.enterprises }}" label="单位" placeholder="请输入单位" data-modal="enterprises" required="true" bind:change="onChange" error="{{ formErr.enterprisesErr }}" error-message="{{ formErr.enterprisesErrMsg }}" />
    <van-field size="large" value="{{ form.licensePlate }}" label="车牌" placeholder="请输入车牌" data-modal="licensePlate" bind:change="onChange" error="{{ formErr.licensePlateErr }}" error-message="{{ formErr.licensePlateErrMsg }}" />
    <van-cell size="large" title="携带物品" bindtap="carryChange">
      <van-switch checked="{{ form.isCarry }}" size="15"/>
    </van-cell>
    <van-field size="large" wx:if="{{ form.isCarry }}" value="{{ form.belongings }}" label="物品" placeholder="请输入物品" data-modal="belongings" required="true" autosize="true" type="textarea" bind:change="onChange" error="{{ formErr.belongingsErr }}" error-message="{{ formErr.belongingsErrMsg }}" />
    <van-cell size="large" wx:if="{{ form.isCarry }}" title="物品照片" required="true" label-class="err-label" use-label-slot>
      <view slot="label">
        <van-uploader file-list="{{ fileList }}" multiple sizeType="compressed" capture="['album', 'camera']" bind:after-read="afterRead" bind:delete="picDelete" />
      {{ formErr.picErrMsg }}
      </view>
    </van-cell>
    <van-cell size="large" title="接待人员" required="true" is-link data-type="front" bindtap="openReceptionistPopup" label="{{ formErr.receptionistErrMsg }}" value="{{ form.receptionistName }}" label-class="err-label" />
    <van-field size="large" value="{{ form.visitReason }}" label="来访事由" placeholder="请输入来访事由" data-modal="visitReason" required="true" autosize="true" type="textarea" bind:change="onChange" error="{{ formErr.visitReasonErr }}" error-message="{{ formErr.visitReasonErrMsg }}" />
  </van-cell-group>
  <view style="text-align: center; margin-bottom: 60rpx; margin-top: 60rpx; width: 80%;">
    <van-button block type="primary" style="width: 80%;" bindtap="submit" custom-class="submit-btn">提交</van-button>
  </view>
</view>

<van-popup show="{{ isPopupShow }}" position="bottom" custom-style="width:750rpx; height: 600rpx;">
  <van-datetime-picker
    type="datetime"
    bind:input="onInput"
    min-date="{{ minDate }}"
    bind:confirm="pickDate"
    bind:cancel="closePopup"
  />
</van-popup>

<van-popup show="{{ isReceptionistShow }}" position="bottom" custom-style="width:750rpx; height: 600rpx;">
  <van-picker show-toolbar columns="{{ receptionistList }}" value-key="fullName" bind:cancel="closePopup" bind:confirm="onReceptionistConfirm" />
</van-popup>

<van-popup show="{{ isLogTypeShow }}" position="bottom" custom-style="width:750rpx; height: 600rpx;">
  <van-picker show-toolbar columns="{{ logTypeList }}" value-key="label" bind:cancel="closePopup" bind:confirm="onLogTypeConfirm" />
</van-popup>

<get-phone show="{{ getPhoneShow }}" bind:getPhone="handleSetWxUser"/>