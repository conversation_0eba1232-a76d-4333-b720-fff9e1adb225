// pages/apply/apply.js
const AUTH = require('../../utils/auth')
import {
  parseTime
} from '../../utils/util'
import {
  request
} from '../../config/request'
var api = require('../../config/api.js')

Page({

  /**
   * 页面的初始数据
   */
  data: {
    form: {
      logType: 'normal',
      logTypeLabel: '',
      visitDate: '',
      enterprises: '',
      licensePlate:'',
      isCarry: false,
      belongings: '',
      receptionist: '',
      receptionistName: '',
      visitReason: '',
      fileIds: [],
    },
    visitors: [{
      name: '',
      identity: '',
      phone: ''
    }],
    formErr: {
    },
    isPopupShow: false,
    minDate: new Date().getTime(),

    isReceptionistShow: false,
    receptionistList: [],

    getPhoneShow: false,
    userInfo: {},

    fileList: [],
    isNormalUser: true,
    isLogTypeShow: false,
    logTypeList: [{
      label: '正常',
      value: 'normal'
    },{
      label: '直接放行',
      value: 'green'
    },{
      label: '补填',
      value: 'later'
    }]
  },

  picDelete(e) {
    const index = e.detail.index
    this.data.form.fileIds.splice(index, 1)
    this.data.fileList.splice(index, 1)
    this.setData({
      form: this.data.form,
      fileList: this.data.fileList
    })
  },

  afterRead(e) { 
    const { file } = e.detail;
    if (file instanceof Array) {
      file.forEach(f => {
        request(api.UploadFile, '', f.url, 'upload').then(res => {
          this.data.form.fileIds.push(res.data.data.fileId)
          this.data.fileList.push(f)
          this.setData({
            form: this.data.form,
            fileList: this.data.fileList
          })
        })
      })
    } else {
      request(api.UploadFile, '', file.url, 'upload').then(res => {
        this.data.form.fileIds.push(res.data.data.fileId)
        this.data.fileList.push(f)
        this.setData({
          form: this.data.form,
          fileList: this.data.fileList
        })
      })
    }
  },

  submit() {
    let checkPass1 = this.checkItem('visitDate', '来访日期不能为空')
    let checkPass2 = this.checkItem('enterprises', '单位不能为空')
    let checkPass3 = this.checkItem('belongings', '物品不能为空')
    let checkPass4 = this.checkItem('receptionist', '接待人员不能为空')
    let checkPass5 = this.checkItem('visitReason', '来访事由不能为空')
    let checkPass6 = true
    if (!this.data.isNormalUser) {
      checkPass6 = this.checkItem('logType', '登记类型不能为空')
    }
    
    let visitCheckPass = true
    this.data.visitors.forEach(v => {
      if (!v.name) {
        visitCheckPass = false
        v.nameErr = true
        v.nameErrMsg = '姓名不能为空'
      } else {
        v.nameErr = false
        v.nameErrMsg = ''
      }
      if (this.data.form.logType === 'normal') {
        const idCardRegex = /(^\d{15}$)|(^\d{18}$)|(^\d{17}(\d|X|x)$)/;
        if (!v.identity) {
          visitCheckPass = false
          v.identityErr = true
          v.identityErrMsg = '身份证不能为空'
        } else if (!idCardRegex.test(v.identity)) {
          visitCheckPass = false
          v.identityErr = true
          v.identityErrMsg = '身份证格式有误'
        } else {
          v.identityErr = false
          v.identityErrMsg = ''
        }
        const r = /^((0\d{2,3}-\d{7,8})|(1([38][0-9]|4[014-9]|[59][0-35-9]|6[2567]|7[0-8])\d{8}))$/
        if (!v.phone) {
          visitCheckPass = false
          v.phoneErr = true
          v.phoneErrMsg = '联系方式不能为空'
        } else if (!r.test(v.phone)) {
          visitCheckPass = false
          v.phoneErr = true
          v.phoneErrMsg = '联系方式格式有误'
        } else {
          v.phoneErr = false
          v.phoneErrMsg = ''
        }
      }
    })
    this.setData({
      visitors: this.data.visitors
    })
    // 检查照片
    if(this.data.form.isCarry && this.data.form.fileIds.length === 0) {
      this.data.formErr.picErrMsg = '请上传图片'
      this.setData({
        formErr: this.data.formErr
      })
      return
    }
    let checkPassCar = true
    if(this.data.form.licensePlate ) {
      checkPassCar = this.carValidator('licensePlate')
    }
    if (!(checkPass1 && checkPass2 && checkPass4 && checkPass5 && checkPass6 &&checkPassCar &&
      (this.data.form.isCarry && checkPass3 || !this.data.form.isCarry) &&
       visitCheckPass)) {
      return
    }

    this.data.form.visitors = this.data.visitors
    request(api.ApplyVisit, this.data.form, '').then(res => {
      if (res.data && res.data.errorCode === '000000') {
        wx.showModal({
          title: '提示',
          content: res.data.errorMsg,
          showCancel: false,
          success (res) {
            if (res.confirm) {
              wx.navigateBack()
            }
          }
        })
      }
    })

  },

  carValidator(item) {
    const licensePlateRegex = /^[京津沪渝冀豫云辽黑湘皖鲁新苏浙赣鄂桂甘晋蒙陕吉闽贵粤青藏川宁琼使领A-Za-z]{1}[A-Za-z]{1}[A-Za-z0-9]{4,5}[A-Za-z0-9挂学警港澳]{1}$/;
    return this.validator(item, licensePlateRegex.test(this.data.form[item]), '车牌号不正确')
  },

  validator(item, test, itemName) {
    if (this.data.form[item] && test) {
      this.data.formErr[item + 'Err'] = false
      this.data.formErr[item + 'ErrMsg'] = ''
      this.setData({
        formErr: this.data.formErr
      })
      return true
    } else {
      this.data.formErr[item + 'Err'] = true
      this.data.formErr[item + 'ErrMsg'] = itemName
      this.setData({
        formErr: this.data.formErr
      })
      return false
    }
  },

  checkItem(item, itemName) {
    if (!this.data.form[item]) {
      this.data.formErr[item + 'Err'] = true
      this.data.formErr[item + 'ErrMsg'] = itemName
      this.setData({
        formErr: this.data.formErr
      })
      return false
    } else {
      this.data.formErr[item + 'Err'] = false
      this.data.formErr[item + 'ErrMsg'] = ''
      this.setData({
        formErr: this.data.formErr
      })
      return true
    }
  },

  loadReceptionist() {
    request(api.ReceptionistList, '', {
      fullName: '',
      current: 1,
      size: 100
    }).then(res => {
      this.setData({
        receptionistList: res.data.records
      })
    })
  },

  onReceptionistConfirm(e) {
    this.data.form.receptionist = e.detail.value.id
    this.data.form.receptionistName = e.detail.value.fullName
    this.setData({
      form: this.data.form,
      isReceptionistShow: false
    })
  },

  onLogTypeConfirm(e) {
    this.data.form.logType = e.detail.value.value
    this.data.form.logTypeLabel = e.detail.value.label
    if (this.data.form.logType !== 'normal') {
      let startTime = new Date().getTime() - 30 * 24 * 3600 * 1000
      this.setData({
        minDate: startTime
      })
    }
    this.setData({
      form: this.data.form,
      isLogTypeShow: false
    })
  },

  openReceptionistPopup() {
    this.setData({
      isReceptionistShow: true
    })
  },

  openLogTypePopup() {
    this.setData({
      isLogTypeShow: true
    })
  },

  onVisitorChange(e) {
    let modal = e.currentTarget.dataset.modal
    let idx = e.currentTarget.dataset.idx
    this.data.visitors[idx][modal] = e.detail
    this.setData({
      visitors: this.data.visitors
    })
  },
  
  pickDate(e) {
    this.data.form.visitDate = parseTime(e.detail, '{y}-{m}-{d} {h}:{i}:{s}')
    this.setData({
      isPopupShow: false,
      form: this.data.form
    })
  },

  closePopup() {
    this.setData({
      isPopupShow: false,
      isReceptionistShow: false,
      isLogTypeShow: false
    })
  },

  openVisitDatePopup() {
    this.setData({
      isPopupShow: true,
    })
  },

  onChange(e) {
    let modal = e.currentTarget.dataset.modal
    this.data.form[modal] = e.detail
    this.setData({
      form: this.data.form
    })
  },

  carryChange() {
    this.data.form.isCarry = !this.data.form.isCarry
    this.setData({
      form: this.data.form
    })
  },

  addVisitor() {
    this.data.visitors.push({
      name: '',
      identity: '',
      phone: ''
    })
    this.setData({
      visitors: this.data.visitors
    })
  },

  deleteVisitor(e) {
    const idx = e.currentTarget.dataset.idx
    this.data.visitors.splice(idx, 1)
    this.setData({
      visitors: this.data.visitors
    })
  },

  setVisitor() {
    this.data.visitors[0].phone = this.data.userInfo.phone
    this.setData({
      visitors: this.data.visitors
    })
  },

  isNormalUser() {
    this.setData({
      isNormalUser: this.data.userInfo.roles.length === 1 && this.data.userInfo.roles[0].id === '1858750179457888256'
    })
  },

  handleSetWxUser(e) {
    this.setData({
      userInfo: e.detail
    })
    this.setVisitor()
    this.isNormalUser()
  },

  getUserApiInfo() {
    request(api.WxUserInfo, '', '').then(res => {
      this.setData({
        userInfo: res.data
      })
      this.isNormalUser()
      if (this.data.userInfo && !this.data.userInfo.phone) {
        this.setData({
          getPhoneShow: true
        })
      } else {
        this.setVisitor()
      }
    })
  },

  async init() {
    await AUTH.loginPromise()
    this.getUserApiInfo()
    this.loadReceptionist()
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {

  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {
    this.init()
  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {

  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {

  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {

  }
})