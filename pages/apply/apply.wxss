/* pages/apply/apply.wxss */
.apply {
  min-height: 100vh;
  background-color: var(--bg-primary);
  display: flex;
  flex-direction: column;
  align-items: center;
  box-sizing: border-box;
  padding: var(--spacing-lg);
}

.title {
  width: 100%;
  text-align: center;
  line-height: 100rpx;
  font-size: var(--font-size-xl);
  font-weight: 600;
  color: var(--primary-color);
  margin-bottom: var(--spacing-lg);
  position: relative;
}

.title::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 100rpx;
  height: 4rpx;
  background-color: var(--primary-color);
  border-radius: var(--radius-full);
}

.form-container {
  width: 100%;
  max-width: 600rpx;
  background-color: var(--bg-card);
  border-radius: var(--radius-lg);
  padding: var(--spacing-lg);
  box-shadow: var(--shadow-md);
  margin-bottom: var(--spacing-xl);
}

.form-item {
  margin-bottom: var(--spacing-lg);
}

.form-label {
  font-size: var(--font-size-md);
  color: var(--text-primary);
  font-weight: 500;
  margin-bottom: var(--spacing-sm);
  display: block;
}

.form-input {
  width: 100%;
  height: 88rpx;
  background-color: var(--bg-tertiary);
  border-radius: var(--radius-md);
  padding: 0 var(--spacing-md);
  font-size: var(--font-size-md);
  color: var(--text-primary);
  border: 2rpx solid var(--border-color);
  transition: all var(--transition-fast);
  box-sizing: border-box;
}

.form-input:focus {
  border-color: var(--primary-color);
  background-color: var(--bg-card);
}

.form-picker {
  width: 100%;
  height: 88rpx;
  background-color: var(--bg-tertiary);
  border-radius: var(--radius-md);
  padding: 0 var(--spacing-md);
  font-size: var(--font-size-md);
  color: var(--text-primary);
  border: 2rpx solid var(--border-color);
  transition: all var(--transition-fast);
  box-sizing: border-box;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.form-picker:focus {
  border-color: var(--primary-color);
  background-color: var(--bg-card);
}

.picker-placeholder {
  color: var(--text-quaternary);
}

.submit-button {
  width: 100%;
  height: 88rpx;
  background-color: var(--primary-color);
  color: var(--text-inverse);
  font-size: var(--font-size-md);
  font-weight: 500;
  border-radius: var(--radius-lg);
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: var(--shadow-md);
  transition: all var(--transition-fast);
  margin-top: var(--spacing-lg);
}

.submit-button:active {
  background-color: var(--primary-dark);
  transform: translateY(2rpx);
  box-shadow: var(--shadow-sm);
}

.err-label {
  color: var(--error-color) !important;
  font-size: var(--font-size-sm);
  margin-top: var(--spacing-xs);
}

.tips {
  width: 100%;
  padding: var(--spacing-md);
  background-color: rgba(0, 102, 204, 0.1);
  border-radius: var(--radius-md);
  margin-bottom: var(--spacing-lg);
}

.tips-title {
  font-size: var(--font-size-md);
  font-weight: 500;
  color: var(--primary-color);
  margin-bottom: var(--spacing-sm);
}

.tips-content {
  font-size: var(--font-size-sm);
  color: var(--text-secondary);
  line-height: var(--line-height-md);
}