/**
 * name: api.js
 * description: request处理基础类
 * author: 181000 王耀
 * date: 2021-11-24
 */
// var cacheKey = require('cacheKey.js')
var apis = require('api.js')
// var config = require('config.js').Config
import qs from 'qs'
import {
  showErrorToast
} from '../utils/toast'

export async function request(api, data, params = '', type = 'request') {
  const noLoginApisSet = apis.NoLoginApis
  if (noLoginApisSet.has(api.url)) {
    return baseRequest(api, data, params)
  } else {
    console.log(api.url+', '+api.method)
    return wx.getStorage({
      key: 'token'
    }).then(u => {
      if (type === 'request') {
        return baseRequest(api, data, params, u.data)
      } else if (type === 'upload') {
        return fileUpload(api, data, params, u.data)
      } else if (type === 'download') {
        return fileDownload(api, params, u.data)
      }
    }).catch(e => {
      console.log("request err"+ api.url + JSON.stringify(e))
      if (e.statusCode === 401) {
        showErrorToast('请先登录')
      } else if (e.statusCode === 403) {
        showErrorToast('无权限')
      } else if (e.data && e.data.message) {
        // showErrorToast(e.data.message)
        wx.showModal({
          title: '提示',
          content: e.data.message,
          showCancel: false
        })
      } else {
        showErrorToast('请求失败')
      }
      return new Promise((resolve, reject) => {
        reject(e)
      })
    })
  }
}

export function baseRequest(api, data, params, token = '') {
  return new Promise((resolve, reject) => {
    wx.showLoading({
      title: '加载中',
    })
    wx.request({
      url: api.url + '?' + qs.stringify(params, {
        indices: false
      }),
      data: data,
      header: {
        'x-access-token': token,
        'content-type': 'application/json'
      },
      dataType: 'json',
      method: api.method,
      success: (res => {
        wx.hideLoading()
        // console.log(JSON.stringify(res))
        if (res.statusCode >= 200 && res.statusCode < 300 ) {
          if (res.data.errorCode && res.data.errorCode !== '000000' && res.data.errorCode !== '403'){
            reject(res)
          }
          //200: 服务端业务处理正常结束
          resolve(res)
        } else {
          console.log('请求失败')
          reject(res)
        }
      }),
      fail: (res => {
        wx.hideLoading()
        console.log(JSON.stringify(res))
        console.log('请求失败')
        reject(res)
      })
    })
  })
}

export function fileUpload(api, data, filePath, token = '') {
  return new Promise((resolve, reject) => {
    wx.showLoading({
      title: '加载中',
    })
    wx.uploadFile({
      url: api.url,
      filePath: filePath,
      name: 'file',
      formData: data,
      dataType: 'json',
      header: {
        'x-access-token': token,
        "Content-Type": "multipart/form-data"
      },
      success: (res => {
        wx.hideLoading()
        console.log(JSON.stringify(res))
        if (res.statusCode === 200) {
          res.data = JSON.parse(res.data)
          //200: 服务端业务处理正常结束
          resolve(res)
        } else {
          console.log('请求失败')
          reject(res)
        }
      }),
      fail: (res => {
        wx.hideLoading()
        console.log(JSON.stringify(res))
        console.log('请求失败')
        reject(res)
      })
    })
  })
}

export function fileDownload(api, params, token = '') {
  return new Promise((resolve, reject) => {
    wx.showLoading({
      title: '加载中',
    })
    wx.downloadFile({
      url: api.url + '?' + qs.stringify(params, {
        indices: false
      }),
      header: {
        'x-access-token': token
      },
      success: (res => {
        wx.hideLoading()
        console.log(JSON.stringify(res))
        if (res.statusCode === 200) {
          //200: 服务端业务处理正常结束
          res.params = params
          resolve(res)
        } else {
          console.log('请求失败')
          reject(res)
        }
      }),
      fail: (res => {
        wx.hideLoading()
        console.log(JSON.stringify(res))
        console.log('请求失败')
        reject(res)
      })
    })
  })
}

export default {
  request,
  baseRequest
}