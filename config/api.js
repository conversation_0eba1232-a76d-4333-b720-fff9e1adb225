var config = require('config.js').Config
// API地址
var WxApiRoot = config.WxApiRoot
// 接口地址
var apis = {
    Login: {
      url: WxApiRoot + '/api/wx/login', // 登录
      method: 'get'
    },
    CheckToken: {
        url: WxApiRoot + '/api/wx/checkToken', // 核验token
        method: 'get'
    },
    WxUserInfo: {
      url: WxApiRoot + '/api/wxUser/info',
      method: 'get'
    },
    UploadFile: {
      url: WxApiRoot + '/api/visitFile/upload',
      method: 'post'
    },
    DownloadFile: {
      url: WxApiRoot + '/api/visitFile/file',
      method: 'get'
    },
    WxUserAvatarUpdate: {
      url: WxApiRoot + '/api/wxUser/avatar',
      method: 'put'
    },
    WxUserNickNameUpdate: {
      url: WxApiRoot + '/api/wxUser/nickName',
      method: 'put'
    },
    DecryptPhone: {
      url: WxApiRoot + '/api/wx/decrypt', // 解密
      method: 'get'
    },
    ApplyVisit: {
      url: WxApiRoot + '/api/visitLog',
      method: 'post'
    },
    ReceptionistList: {
      url: WxApiRoot + '/api/visitLog/receptionist',
      method: 'get'
    },
    MyVisitLog: {
      url: WxApiRoot + '/api/visitLog',
      method: 'get'
    },
    VisitLogDetail: {
      url: WxApiRoot + '/api/visitLog/detail',
      method: 'get'
    },
    VisitLogHandle: {
      url: WxApiRoot + '/api/visitHandle/handle',
      method: 'put'
    },
}

var noLoginApis = new Set([
    apis.Login.url,
])
apis['NoLoginApis'] = noLoginApis

module.exports = apis