{"description": "项目配置文件，详见文档：https://developers.weixin.qq.com/miniprogram/dev/devtools/projectconfig.html", "setting": {"urlCheck": false, "es6": true, "enhance": true, "postcss": false, "preloadBackgroundData": false, "minified": true, "newFeature": true, "coverView": true, "nodeModules": false, "autoAudits": false, "showShadowRootInWxmlPanel": true, "scopeDataCheck": false, "uglifyFileName": false, "checkInvalidKey": true, "checkSiteMap": true, "uploadWithSourceMap": true, "compileHotReLoad": true, "lazyloadPlaceholderEnable": false, "useMultiFrameRuntime": false, "useApiHook": false, "useApiHostProcess": false, "babelSetting": {"ignore": [], "disablePlugins": [], "outputPath": ""}, "enableEngineNative": false, "useIsolateContext": true, "userConfirmedBundleSwitch": false, "packNpmManually": false, "packNpmRelationList": [], "minifyWXSS": true, "disableUseStrict": false, "minifyWXML": true, "showES6CompileOption": false, "useCompilerPlugins": false, "condition": false, "compileWorklet": false, "localPlugins": false, "swc": false, "disableSWC": true}, "compileType": "miniprogram", "libVersion": "3.8.11", "appid": "wx602a10634c53e5c4", "projectname": "%E5%A4%A9%E4%BD%BF%E7%AB%A5%E8%A3%85", "simulatorType": "wechat", "simulatorPluginLibVersion": {}, "packOptions": {"ignore": [], "include": []}, "editorSetting": {"tabIndent": "insertSpaces", "tabSize": 2}, "condition": {"miniprogram": {"list": [{"name": "home", "pathName": "pages/index/index", "query": "", "scene": null}, {"name": "商品详情页面", "pathName": "pages/goods-details/index", "query": "id=134&inviter_id=456", "scene": 1011}, {"name": "商品详情页面", "pathName": "pages/goods-details/index", "query": "id= 6761", "scene": null}, {"name": "砍价详情页面", "pathName": "pages/goods-details/index", "query": "id=6765", "scene": null}, {"name": "砍价详情页面", "pathName": "pages/goods-details/index", "query": "id=6765&kjJoinUid=701677", "scene": null}, {"name": "拼团", "pathName": "pages/goods-details/index", "query": "id=6761", "scene": null}, {"name": "生成海报", "pathName": "pages/goods-details/poster", "query": "goodsid=6761", "scene": null}, {"name": "notice", "pathName": "pages/notice/index", "query": "", "scene": null}, {"name": "带参数进首页", "pathName": "pages/index/index", "query": "a=1&b=2&c=3", "scene": null}]}}}