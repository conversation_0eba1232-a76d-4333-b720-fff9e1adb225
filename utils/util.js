import {
  request
} from '../config/request'
var api = require('../config/api.js')

/**
 * 初始化字典
 * @param {*} dictNameList 
 */
export async function initDict(dictNameList) {
  const dictMap = {}
  for (let dictName of dictNameList) {
    let list = await getDictDetailList(dictName)
    let map = listToMap(list, 'value', 'label')
    dictMap[dictName] = {
      item: list,
      label: map
    }
  }
  return dictMap
}

/**
 * 加载字典
 * @param {*} dictName 
 */
export async function getDictDetailList(dictName) {
  const res = await request(api.DictQuery, {}, {
    dictName: dictName,
    page: 0,
    size: 50
  })
  return res.data.content
}

/**
 * 列表转字典
 * @param {*} list 
 * @param {*} key 
 */
export function listToMap(list, key, value) {
  let map = {}
  list.forEach(l => {
    if (value) {
      map[l[key]] = l[value]
    } else {
      map[l[key]] = l
    }
  })
  return map
}

/**
 * 扫码签到
 */
export function scan() {
  wx.scanCode({
    success(res) {
      console.log(res)
      const result = res.result
      const type = result.split(":")[0]
      const id = result.split(":")[1]
      if (type === 'activity') {
        request(api.ChamberActivityApplySign, {
          activity: {
            activityId: id
          }
        }, {}).then(res => {
          wx.showToast({
            title: '签到成功',
            icon: 'success',
            duration: 2000
          })
        })
      }
    }
  })
}
/**
 * Parse the time to string
 * @param {(Object|string|number)} time
 * @param {string} cFormat
 * @returns {string}
 */
export function parseTime(time, cFormat) {
  if (arguments.length === 0) {
    return null
  }
  const format = cFormat || '{y}-{m}-{d} {h}:{i}:{s}'
  let date
  if (typeof time === 'undefined' || time === null || time === 'null') {
    return ''
  } else if (typeof time === 'object') {
    date = time
  } else {
    if ((typeof time === 'string') && (/^[0-9]+$/.test(time))) {
      time = parseInt(time)
    }
    if ((typeof time === 'number') && (time.toString().length === 10)) {
      time = time * 1000
    }
    date = new Date(time)
  }
  const formatObj = {
    y: date.getFullYear(),
    m: date.getMonth() + 1,
    d: date.getDate(),
    h: date.getHours(),
    i: date.getMinutes(),
    s: date.getSeconds(),
    a: date.getDay()
  }
  const time_str = format.replace(/{(y|m|d|h|i|s|a)+}/g, (result, key) => {
    let value = formatObj[key]
    // Note: getDay() returns 0 on Sunday
    if (key === 'a') { return ['日', '一', '二', '三', '四', '五', '六'][value ] }
    if (result.length > 0 && value < 10) {
      value = '0' + value
    }
    return value || 0
  })
  return time_str
}

export default {
  initDict,
  scan,
  listToMap,
  parseTime
}