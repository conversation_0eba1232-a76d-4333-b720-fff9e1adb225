
import { request } from '../config/request'
var api = require('../config/api.js')
var config = require('../config/config.js').Config

async function checkSession(){
  return new Promise((resolve, reject) => {
    wx.checkSession({
      success() {
        return resolve(true)
      },
      fail() {
        return resolve(false)
      }
    })
  })
}

// 检测登录状态，返回 true / false
async function checkHasLogined() {
  const token = wx.getStorageSync('token')
  if (!token) {
    return false
  }
  const loggined = await checkSession()
  if (!loggined) {
    wx.removeStorageSync('token')
    return false
  }
  const checkTokenRes = await request(api.CheckToken, '', '')
  if (checkTokenRes.data && checkTokenRes.data.errorCode && checkTokenRes.data.errorCode === '403') {
    wx.removeStorageSync('token')
    return false
  }
  return true
}

async function wxaCode(){
  return new Promise((resolve, reject) => {
    wx.login({
      success(res) {
        return resolve(res.code)
      },
      fail() {
        wx.showToast({
          title: '获取code失败',
          icon: 'none'
        })
        return resolve('获取code失败')
      }
    })
  })
}

async function login(){
  const _this = this
  wx.login({
    success: function (res) {
      request(api.Login, '', { code: res.code }).then(function (res) {   
        wx.setStorageSync('token', res.data.token)
        wx.setStorageSync('openid', res.data.openId)
      })
    }
  })
}

async function loginPromise(){
  const _this = this
  const hasLogin = await _this.checkHasLogined()
  if (!hasLogin) {
    const code = await _this.wxaCode()
    const res = await request(api.Login, '', { code: code })
    wx.setStorageSync('token', res.data.token)
    wx.setStorageSync('openid', res.data.openId)
    wx.setStorageSync('user', res.data)
  }
}

function loginOut(){
  wx.removeStorageSync('token')
  wx.removeStorageSync('openid')
}

async function checkAndAuthorize (scope) {
  return new Promise((resolve, reject) => {
    wx.getSetting({
      success(res) {
        if (!res.authSetting[scope]) {
          wx.authorize({
            scope: scope,
            success() {
              resolve() // 无返回参数
            },
            fail(e){
              console.error(e)
              wx.showModal({
                title: '无权操作',
                content: '需要获得您的授权',
                showCancel: false,
                confirmText: '立即授权',
                confirmColor: '#e64340',
                success(res) {
                  wx.openSetting();
                },
                fail(e){
                  console.error(e)
                  reject(e)
                },
              })
            }
          })
        } else {
          resolve() // 无返回参数
        }
      },
      fail(e){
        console.error(e)
        reject(e)
      }
    })
  })  
}

module.exports = {
  checkHasLogined: checkHasLogined,
  wxaCode: wxaCode,
  login: login,
  loginOut: loginOut,
  loginPromise: loginPromise
}