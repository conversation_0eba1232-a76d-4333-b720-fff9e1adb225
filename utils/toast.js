function showErrorToast(title, duration = 2000) {
  wx.showToast({
    title: title,
    icon: 'error',
    duration: duration
  })
}

function showSuccessToast(title, duration = 2000) {
  wx.showToast({
    title: title,
    icon: 'success',
    duration: duration
  })
}

function showNoneToast(title, duration = 2000) {
  wx.showToast({
    title: title,
    icon: 'none',
    duration: duration
  })
}
// success	显示成功图标，此时 title 文本最多显示 7 个汉字长度
// error	显示失败图标，此时 title 文本最多显示 7 个汉字长度
// loading	显示加载图标，此时 title 文本最多显示 7 个汉字长度
// none	不显示图标，此时 title 文本最多可显示两行，1.9.0及以上版本支持

module.exports = {
  showErrorToast
}
