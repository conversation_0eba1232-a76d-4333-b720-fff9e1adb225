# 微信小程序商城

微信小程序商城，微信小程序微店，长期维护版本，欢迎大家踊跃提交贡献代码；

使用说明和常见问题，可参阅下面的说明，如还有疑问，可访问工厂官网 [https://www.it120.cc/](https://www.it120.cc/) 寻求帮助！

新增直播带货支持，具体详见使用说明

# 今日头条/抖音小程序版本

本项目的今日头条/抖音小程序版本，请移步至下面的地址：

[https://github.com/EastWorld/tt-app-mall](https://github.com/EastWorld/tt-app-mall)

## 扫码体验

<img src="https://dcdn.it120.cc/2022/12/31/0215c085-d4d1-43e4-bd7d-0e7336eaa661.jpeg" width="200px">

## 详细配置/使用教程

[https://www.it120.cc/help/ikfe2k.html](https://www.it120.cc/help/ikfe2k.html)

**遇到使用问题？**

[点击这里找答案，可用关键词搜索](https://www.it120.cc/help.html)

## 其他优秀开源模板推荐
- [天使童装（uni-app版本）](https://github.com/gooking/uni-app-mall) [码云镜像](https://gitee.com/javazj/uni-app-mall)
- [舔果果小铺（升级版）](https://github.com/gooking/TianguoguoXiaopu)
- [面馆风格小程序](https://gitee.com/javazj/noodle_shop_procedures)
- [AI名片](https://github.com/gooking/visitingCard)
- [仿海底捞订座排队 (uni-app)](https://github.com/gooking/dingzuopaidui) [码云镜像](https://gitee.com/javazj/dingzuopaidui)

## 联系作者

| 微信好友 | QQ好友 |
| :------: | :------: |
| <img src="https://dcdn.it120.cc/2021/09/13/61a80363-9085-4a10-9447-e276a3d40ab3.jpeg" width="200px"> | <img src="https://dcdn.it120.cc/2021/09/13/08a598d8-8186-4159-9930-2e4908accc5e.png" width="200px"> |

## 本项目使用了下面的组件，在此鸣谢

- [接口 SDK](https://github.com/gooking/apifm-wxapi)

- [api工厂](https://admin.it120.cc)

- [vant-weapp](https://youzan.github.io/vant-weapp)

- [小程序富文本插件（html 渲染）](https://github.com/jin-yufeng/mp-html)

- [小程序海报组件-生成朋友圈分享海报并生成图片](https://github.com/jasondu/wxa-plugin-canvas)

- [Apache ECharts](https://github.com/ecomfe/echarts-for-weixin)

底部ICON图标使用：
https://www.iconfont.cn/collections/detail?spm=a313x.7781069.0.da5a778a4&cid=18904

  
## 如何升级到最新版

- 小程序程序的修改和您后台的数据是独立的，所以不用担心您会丢失数据
- 先把你开发工具下的现有版本程序备份
- 下载最新版的程序，直接覆盖您本地的程序
- 用开发工具修改域名 mall 为你自己的域名
- 开发工具里面上传代码提交微信审核
- 审核通过后，小程序后台去发布新版本即可
- 用户无需重新扫码，关闭小程序重新打开就是新版本了
