.apifmLogin {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.3);
  z-index: 99999999999;
}

.apifmLogin .s-b {
  width: 70%;
  margin: 0 auto;
  height: 600rpx;
  background-color: #fff;
  margin-top: 40%;
  border-radius: 10rpx;
}

.apifmLogin .s-b .s-l-b {
  text-align: center;
  width: 80%;
  border-bottom: 1px solid #f5f5f5;
  margin: 0 auto;
  padding-top: 20px;
}

.apifmLogin .s-b .s-l-b image {
  width: 120rpx;
  height: 120rpx;
  border-radius: 50%;
  display: block;
  margin: 0 auto;
}
.apifmLogin .s-b .s-l-b text {
  font-size: 32rpx;
  margin-top: 20rpx;
  margin-bottom: 20rpx;
  color: #333;
}

.apifmLogin .s-b .s-t-b {
  margin-top: 20rpx;
  margin-bottom: 40rpx;
  text-align: center;
}

.apifmLogin .s-b .s-t-b .s-t-n {
  font-size: 30rpx;
  color: #333;
  width: 80%;
  margin: 0 auto;
}

.apifmLogin .s-b .s-t-b .s-t-i {
  width: 80%;
  margin: 0 auto;
  font-size: 24rpx;
  color: #999;
  margin-top: 10rpx;
}

.apifmLogin .s-b .s-t-b .s-t-i text {
  margin-right: 10rpx;
  font-size: 26rpx;
}

.apifmLogin .s-b button::after {
  border-radius: 4rpx;
  border: 0;
}

.apifmLogin .s-b .l {
  width: 80%;
  height: 70rpx;
  line-height: 70rpx;
  font-size: 30rpx;
  background-color: #04be01;
  color: #fff;
}

.apifmLogin .s-b .c {
  margin-top: 20rpx;
  margin-bottom: 20rpx;
  width: 80%;
  height: 70rpx;
  line-height: 70rpx;
  font-size: 30rpx;
  background-color: #ccc;
  color: #333;
}