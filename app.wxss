@import "/miniprogram_npm/@vant/weapp/common/index.wxss";

/* CSS变量定义 */
page {
  /* 主题颜色 */
  --primary-color: #0066cc;
  --primary-light: #4d94ff;
  --primary-dark: #0052a3;
  --secondary-color: #34c759;
  --secondary-light: #5dd879;
  --secondary-dark: #2aa048;
  --accent-color: #ff9500;
  --accent-light: #ffb143;
  --accent-dark: #cc7700;
  
  /* 背景颜色 */
  --bg-primary: #f8f9fa;
  --bg-secondary: #ffffff;
  --bg-tertiary: #f1f3f5;
  --bg-card: #ffffff;
  --bg-overlay: rgba(0, 0, 0, 0.5);
  
  /* 文本颜色 */
  --text-primary: #212529;
  --text-secondary: #495057;
  --text-tertiary: #6c757d;
  --text-quaternary: #adb5bd;
  --text-inverse: #ffffff;
  
  /* 边框颜色 */
  --border-color: #e9ecef;
  --border-light: #f1f3f5;
  --border-dark: #dee2e6;
  
  /* 状态颜色 */
  --success-color: #34c759;
  --warning-color: #ff9500;
  --error-color: #ff3b30;
  --info-color: #5ac8fa;
  
  /* 阴影 */
  --shadow-sm: 0 2rpx 4rpx rgba(0, 0, 0, 0.05);
  --shadow-md: 0 4rpx 8rpx rgba(0, 0, 0, 0.1);
  --shadow-lg: 0 8rpx 16rpx rgba(0, 0, 0, 0.15);
  
  /* 圆角 */
  --radius-sm: 8rpx;
  --radius-md: 16rpx;
  --radius-lg: 24rpx;
  --radius-xl: 32rpx;
  --radius-full: 999rpx;
  
  /* 间距 */
  --spacing-xs: 8rpx;
  --spacing-sm: 16rpx;
  --spacing-md: 24rpx;
  --spacing-lg: 32rpx;
  --spacing-xl: 48rpx;
  
  /* 字体大小 */
  --font-size-xs: 20rpx;
  --font-size-sm: 24rpx;
  --font-size-md: 28rpx;
  --font-size-lg: 32rpx;
  --font-size-xl: 36rpx;
  --font-size-xxl: 40rpx;
  
  /* 行高 */
  --line-height-sm: 1.2;
  --line-height-md: 1.5;
  --line-height-lg: 1.8;
  
  /* 过渡动画 */
  --transition-fast: 0.2s ease;
  --transition-normal: 0.3s ease;
  --transition-slow: 0.5s ease;
  
  background-color: var(--bg-primary);
  font-family: -apple-system, BlinkMacSystemFont, 'Helvetica Neue', Helvetica,
    Segoe UI, Arial, Roboto, 'PingFang SC', 'miui', 'Hiragino Sans GB', 'Microsoft Yahei',
    sans-serif;
  color: var(--text-primary);
  line-height: var(--line-height-md);
  /* 渐变色变量 */
  --gradient-blue: linear-gradient(90deg, #007aff, #00d4ff);
  --gradient-blue-alt: linear-gradient(135deg, #007aff, #00d4ff);
  --gradient-red-orange: linear-gradient(90deg, #ff6b6b, #ffa502);
  --gradient-light-blue: linear-gradient(to bottom, #f5f7fa, #e4edf9);
}

.container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: space-between;
  box-sizing: border-box;
  padding: var(--spacing-md);
}

.space {
  height: var(--spacing-md);
  background-color: var(--bg-tertiary);
}

.safeAreaOldPaddingBttom {
  padding-bottom: env(safe-area-inset-bottom);
}
.safeAreaNewPaddingBttom{
  padding-bottom: constant(safe-area-inset-bottom);
}

.safeAreaOldMarginBttom {
  margin-bottom: env(safe-area-inset-bottom);
}
.safeAreaNewMarginBttom{
  margin-bottom: constant(safe-area-inset-bottom);
}

.no-data {
  width: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  padding: var(--spacing-lg) 0;
}
.no-data .line {
  width:132rpx;
  height:2rpx;
  background: var(--text-quaternary);
}
.no-data .txt {
  font-size: var(--font-size-sm);
  color: var(--text-quaternary);
  margin: 0 var(--spacing-sm);
}
.ad-img {
  width: 100vw;
}
.badge {
  position: absolute;
  top: 0;
  right: 0;
  box-sizing: border-box;
  padding: 6rpx;
  color: var(--text-inverse);
  font-size: var(--font-size-xs);
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: var(--error-color);
  border: 1rpx solid var(--bg-secondary);
  border-radius: var(--radius-full);
}
.vw100 {
  width: 100vw !important;
}

.block-btn {
  padding: 0 var(--spacing-lg);
}

.safe-bottom-box {
  position: fixed;
  bottom: calc(env(safe-area-inset-bottom) / 2);
  left: 0;
  width: 100vw;
}
.characteristic {
  padding: 0 var(--spacing-md);
  font-size: var(--font-size-sm);
  color: var(--text-quaternary);
}

.tips {
  width: 750rpx;
  text-align: center;
  line-height: 50rpx;
  font-size: var(--font-size-xs);
  margin-top: var(--spacing-md);
  padding-bottom: var(--spacing-xl);
  color: var(--text-quaternary);
}

/* 通用按钮样式 */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: var(--spacing-md) var(--spacing-lg);
  border-radius: var(--radius-md);
  font-size: var(--font-size-md);
  font-weight: 500;
  transition: all var(--transition-fast);
  border: none;
  outline: none;
}

.btn-primary {
  background-color: var(--primary-color);
  color: var(--text-inverse);
}

.btn-primary:hover {
  background-color: var(--primary-light);
}

.btn-primary:active {
  background-color: var(--primary-dark);
}

.btn-secondary {
  background-color: var(--secondary-color);
  color: var(--text-inverse);
}

.btn-secondary:hover {
  background-color: var(--secondary-light);
}

.btn-secondary:active {
  background-color: var(--secondary-dark);
}

.btn-outline {
  background-color: transparent;
  color: var(--primary-color);
  border: 2rpx solid var(--primary-color);
}

.btn-outline:hover {
  background-color: var(--primary-color);
  color: var(--text-inverse);
}

/* 通用卡片样式 */
.card {
  background-color: var(--bg-card);
  border-radius: var(--radius-lg);
  padding: var(--spacing-lg);
  margin-bottom: var(--spacing-md);
  box-shadow: var(--shadow-sm);
}

.card-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: var(--spacing-md);
  padding-bottom: var(--spacing-md);
  border-bottom: 2rpx solid var(--border-color);
}

.card-title {
  font-size: var(--font-size-lg);
  font-weight: 600;
  color: var(--text-primary);
}

.card-body {
  color: var(--text-secondary);
}

/* 通用列表样式 */
.list-item {
  display: flex;
  align-items: center;
  padding: var(--spacing-md) 0;
  border-bottom: 2rpx solid var(--border-color);
}

.list-item:last-child {
  border-bottom: none;
}

.list-item-content {
  flex: 1;
}

.list-item-title {
  font-size: var(--font-size-md);
  color: var(--text-primary);
  margin-bottom: var(--spacing-xs);
}

.list-item-desc {
  font-size: var(--font-size-sm);
  color: var(--text-tertiary);
}

.list-item-arrow {
  width: 32rpx;
  height: 32rpx;
  margin-left: var(--spacing-md);
}