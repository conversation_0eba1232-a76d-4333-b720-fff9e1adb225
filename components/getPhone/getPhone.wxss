/* components/getPhone.wxss */

/* 授权手机号弹窗样式 */
.get-phone-popup {
  background-color: var(--bg-card);
  border-radius: var(--radius-xl) var(--radius-xl) 0 0;
  padding: var(--spacing-xl);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.popup-title {
  font-size: var(--font-size-lg);
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: var(--spacing-lg);
  text-align: center;
}

.popup-desc {
  font-size: var(--font-size-md);
  color: var(--text-tertiary);
  margin-bottom: var(--spacing-xl);
  text-align: center;
  line-height: var(--line-height-md);
}

.get-phone-button {
  width: 80%;
  height: 88rpx;
  background-color: var(--primary-color);
  color: var(--text-inverse);
  font-size: var(--font-size-md);
  font-weight: 500;
  border-radius: var(--radius-lg);
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: var(--shadow-md);
  transition: all var(--transition-fast);
  margin-top: var(--spacing-lg);
}

.get-phone-button:active {
  background-color: var(--primary-dark);
  transform: translateY(2rpx);
  box-shadow: var(--shadow-sm);
}

.cancel-button {
  width: 80%;
  height: 88rpx;
  background-color: transparent;
  color: var(--text-tertiary);
  font-size: var(--font-size-md);
  font-weight: 500;
  border-radius: var(--radius-lg);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-top: var(--spacing-md);
  transition: all var(--transition-fast);
}

.cancel-button:active {
  background-color: var(--bg-tertiary);
}