// components/getPhone.js
import {
  request
} from '../../config/request'
var api = require('../../config/api.js')

Component({
  /**
   * 组件的属性列表
   */
  properties: {
    show: {
      type: Boolean,
      value: false
    }
  },

  /**
   * 组件的初始数据
   */
  data: {

  },

  /**
   * 组件的方法列表
   */
  methods: {
    onClose() {
      this.setData({ show: false });
    },
    getPhoneNumber(e) {
      if (e.detail.errMsg === 'getPhoneNumber:ok') {
        request(api.DecryptPhone, {
          encryptedData: e.detail.encryptedData,
          iv: e.detail.iv
        }).then(res => {
          if (res.data !== undefined && res.data !== null && res.data !== '') {
            wx.setStorageSync('user', res.data)
            this.triggerEvent('getPhone', res.data)
            this.onClose()
          } else {
            wx.showToast({
              title: '无认证信息',
              icon: 'error',
              duration: 2000
            })
          }
        }).catch(err2 => {
          console.log(JSON.stringify(err2))
        })
      }
    }
  }
})
