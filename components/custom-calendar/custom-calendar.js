// components/custom-calendar/custom-calendar.js
Component({
  /**
   * 组件的属性列表
   */
  properties: {
    selectedDate: {
      type: String,
      value: ''
    },
    minDate: {
      type: String,
      value: ''
    },
    maxDate: {
      type: String,
      value: ''
    },
    eventDates: {
      type: Array,
      value: []
    }
  },

  /**
   * 组件的初始数据
   */
  data: {
    currentYear: 2025,
    currentMonth: 1,
    calendarDays: [],
    today: ''
  },

  /**
   * 组件的方法列表
   */
  methods: {
    /**
     * 初始化日历
     */
    initCalendar() {
      const today = new Date();
      const todayStr = this.formatDate(today);
      
      this.setData({
        currentYear: today.getFullYear(),
        currentMonth: today.getMonth() + 1,
        today: todayStr
      });
      
      this.generateCalendar();
    },

    /**
     * 生成日历数据
     */
    generateCalendar() {
      const { currentYear, currentMonth } = this.data;
      const firstDay = new Date(currentYear, currentMonth - 1, 1);
      const lastDay = new Date(currentYear, currentMonth, 0);
      const daysInMonth = lastDay.getDate();
      
      // 获取第一天是星期几（0=周日，1=周一...）
      let firstDayOfWeek = firstDay.getDay();
      // 转换为周一开始（0=周一，1=周二...）
      firstDayOfWeek = firstDayOfWeek === 0 ? 6 : firstDayOfWeek - 1;
      
      const calendarDays = [];
      
      // 添加上个月的日期
      const prevMonth = currentMonth === 1 ? 12 : currentMonth - 1;
      const prevYear = currentMonth === 1 ? currentYear - 1 : currentYear;
      const prevMonthLastDay = new Date(prevYear, prevMonth, 0).getDate();
      
      for (let i = firstDayOfWeek - 1; i >= 0; i--) {
        const day = prevMonthLastDay - i;
        const date = new Date(prevYear, prevMonth - 1, day);
        calendarDays.push({
          day: day,
          date: this.formatDate(date),
          isCurrentMonth: false,
          isSelected: false,
          isToday: false,
          hasEvent: this.hasEvent(this.formatDate(date))
        });
      }
      
      // 添加当前月的日期
      for (let day = 1; day <= daysInMonth; day++) {
        const date = new Date(currentYear, currentMonth - 1, day);
        const dateStr = this.formatDate(date);
        calendarDays.push({
          day: day,
          date: dateStr,
          isCurrentMonth: true,
          isSelected: dateStr === this.data.selectedDate,
          isToday: dateStr === this.data.today,
          hasEvent: this.hasEvent(dateStr)
        });
      }
      
      // 添加下个月的日期，补齐6行
      const totalCells = 42; // 6行 × 7列
      const remainingCells = totalCells - calendarDays.length;
      const nextMonth = currentMonth === 12 ? 1 : currentMonth + 1;
      const nextYear = currentMonth === 12 ? currentYear + 1 : currentYear;
      
      for (let day = 1; day <= remainingCells; day++) {
        const date = new Date(nextYear, nextMonth - 1, day);
        calendarDays.push({
          day: day,
          date: this.formatDate(date),
          isCurrentMonth: false,
          isSelected: false,
          isToday: false,
          hasEvent: this.hasEvent(this.formatDate(date))
        });
      }
      
      this.setData({
        calendarDays: calendarDays
      });
    },

    /**
     * 检查日期是否有事件
     */
    hasEvent(dateStr) {
      return this.data.eventDates.includes(dateStr);
    },

    /**
     * 格式化日期
     */
    formatDate(date) {
      const year = date.getFullYear();
      const month = String(date.getMonth() + 1).padStart(2, '0');
      const day = String(date.getDate()).padStart(2, '0');
      return `${year}-${month}-${day}`;
    },

    /**
     * 上一个月
     */
    prevMonth() {
      let { currentYear, currentMonth } = this.data;
      if (currentMonth === 1) {
        currentMonth = 12;
        currentYear--;
      } else {
        currentMonth--;
      }
      
      this.setData({
        currentYear: currentYear,
        currentMonth: currentMonth
      });
      
      this.generateCalendar();
    },

    /**
     * 下一个月
     */
    nextMonth() {
      let { currentYear, currentMonth } = this.data;
      if (currentMonth === 12) {
        currentMonth = 1;
        currentYear++;
      } else {
        currentMonth++;
      }
      
      this.setData({
        currentYear: currentYear,
        currentMonth: currentMonth
      });
      
      this.generateCalendar();
    },

    /**
     * 选择日期
     */
    selectDate(e) {
      const { date, index } = e.currentTarget.dataset;
      const calendarDays = this.data.calendarDays;
      
      // 清除之前的选中状态
      calendarDays.forEach(day => {
        day.isSelected = false;
      });
      
      // 设置新的选中状态
      calendarDays[index].isSelected = true;
      
      this.setData({
        calendarDays: calendarDays,
        selectedDate: date
      });
      
      // 触发选择事件
      this.triggerEvent('dateSelect', {
        date: date
      });
    }
  },

  /**
   * 组件生命周期
   */
  lifetimes: {
    attached() {
      this.initCalendar();
    }
  },

  /**
   * 监听属性变化
   */
  observers: {
    'selectedDate': function(newVal) {
      this.generateCalendar();
    }
  }
})
