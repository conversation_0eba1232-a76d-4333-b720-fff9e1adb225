/* components/custom-calendar/custom-calendar.wxss */
.calendar-container {
  background-color: #fff;
  border-radius: 15px;
  padding: 20px;
  margin: 20px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

/* 日历头部 */
.calendar-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding: 0 10px;
}

.month-year {
  font-size: 18px;
  font-weight: 600;
  color: #333;
}

/* 星期标题 */
.week-header {
  display: flex;
  margin-bottom: 15px;
}

.week-day {
  flex: 1;
  text-align: center;
  font-size: 14px;
  color: #666;
  font-weight: 500;
  padding: 10px 0;
}

/* 日期网格 */
.calendar-grid {
  display: flex;
  flex-wrap: wrap;
}

.calendar-day {
  width: calc(100% / 7);
  height: 50px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  position: relative;
  cursor: pointer;
  transition: all 0.2s ease;
}

.calendar-day:active {
  background-color: #f5f5f5;
  border-radius: 8px;
}

.day-number {
  font-size: 16px;
  color: #333;
  font-weight: 500;
  line-height: 1;
}

/* 其他月份的日期 */
.calendar-day.other-month .day-number {
  color: #ccc;
}

/* 今天 */
.calendar-day.today {
  background-color: #4A90E2;
  border-radius: 50%;
  color: #fff;
}

.calendar-day.today .day-number {
  color: #fff;
  font-weight: 600;
}

/* 选中的日期 */
.calendar-day.selected {
  background-color: #4A90E2;
  border-radius: 50%;
}

.calendar-day.selected .day-number {
  color: #fff;
  font-weight: 600;
}

/* 有事件的日期 */
.calendar-day.has-event .event-dot {
  width: 4px;
  height: 4px;
  background-color: #4A90E2;
  border-radius: 50%;
  position: absolute;
  bottom: 8px;
}

.calendar-day.selected.has-event .event-dot,
.calendar-day.today.has-event .event-dot {
  background-color: #fff;
}

/* 禁用状态 */
.calendar-day.disabled {
  opacity: 0.3;
  pointer-events: none;
}

.calendar-day.disabled .day-number {
  color: #ccc;
}
