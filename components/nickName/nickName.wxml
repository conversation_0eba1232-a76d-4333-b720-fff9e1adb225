<!--components/nickName/nickName.wxml-->
<van-popup
  show="{{ show }}"
  position="bottom"
  custom-style="height: 40%; border-radius: 32rpx 32rpx 0 0;"
  bind:close="onClose"
>
  <view class="nick-name-popup">
    <view class="popup-title">修改昵称</view>
    <view class="popup-desc">请输入您希望使用的昵称</view>
    
    <view class="input-container">
      <input
        class="nick-name-input"
        type="nickname"
        model:value="{{ nickName }}"
        placeholder="请输入昵称"
        bind:input="onChange"
      />
    </view>
    
    <button class="update-button" bindtap="update">确定修改</button>
    <button class="cancel-button" bindtap="onClose">取消</button>
  </view>
</van-popup>
