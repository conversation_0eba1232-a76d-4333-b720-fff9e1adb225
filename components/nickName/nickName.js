// components/nickName/nickName.js
import {
  request
} from '../../config/request'
var api = require('../../config/api.js')

Component({
  /**
   * 组件的属性列表
   */
  properties: {
    nickName: {
      type: String,
      value: ''
    },
    show: {
      type: Boolean,
      value: false
    }
  },

  /**
   * 组件的初始数据
   */
  data: {

  },

  /**
   * 组件的方法列表
   */
  methods: {
    onClose() {
      this.setData({ show: false });
    },
    onChange(e) {
      console.log(e.detail)
      this.setData({
        nickName: e.detail
      })
    }, 
    update() {
      if (!this.data.nickName) {
        wx.showToast({
          title: '请输入微信昵称',
          icon: 'error',
          duration: 2000
        })
        return
      }
      request(api.WxUserNickNameUpdate, {
        nickName: this.data.nickName
      }).then(res => {
        if (res.data.data !== undefined && res.data.data !== null && res.data.data !== '') {
          wx.setStorageSync('user', res.data.data)
          this.triggerEvent('getNickName', res.data.data)
          this.onClose()
        } else {
          wx.showToast({
            title: '昵称修改失败',
            icon: 'error',
            duration: 2000
          })
        }
      })
    }
  }
})
