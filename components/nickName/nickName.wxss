/* components/nickName/nickName.wxss */

/* 修改昵称弹窗样式 */
.nick-name-popup {
  background-color: var(--bg-card);
  border-radius: var(--radius-xl) var(--radius-xl) 0 0;
  padding: var(--spacing-xl);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.popup-title {
  font-size: var(--font-size-lg);
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: var(--spacing-lg);
  text-align: center;
}

.popup-desc {
  font-size: var(--font-size-md);
  color: var(--text-tertiary);
  margin-bottom: var(--spacing-lg);
  text-align: center;
  line-height: var(--line-height-md);
}

.input-container {
  width: 80%;
  margin-bottom: var(--spacing-lg);
}

.nick-name-input {
  width: 100%;
  height: 88rpx;
  background-color: var(--bg-tertiary);
  border-radius: var(--radius-md);
  padding: 0 var(--spacing-md);
  font-size: var(--font-size-md);
  color: var(--text-primary);
  border: 2rpx solid var(--border-color);
  transition: all var(--transition-fast);
}

.nick-name-input:focus {
  border-color: var(--primary-color);
  background-color: var(--bg-card);
}

.update-button {
  width: 80%;
  height: 88rpx;
  background-color: var(--primary-color);
  color: var(--text-inverse);
  font-size: var(--font-size-md);
  font-weight: 500;
  border-radius: var(--radius-lg);
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: var(--shadow-md);
  transition: all var(--transition-fast);
  margin-top: var(--spacing-lg);
}

.update-button:active {
  background-color: var(--primary-dark);
  transform: translateY(2rpx);
  box-shadow: var(--shadow-sm);
}

.cancel-button {
  width: 80%;
  height: 88rpx;
  background-color: transparent;
  color: var(--text-tertiary);
  font-size: var(--font-size-md);
  font-weight: 500;
  border-radius: var(--radius-lg);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-top: var(--spacing-md);
  transition: all var(--transition-fast);
}

.cancel-button:active {
  background-color: var(--bg-tertiary);
}